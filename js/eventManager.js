/**
 * EventManager - Handles random events, crises, and player choices for Pooristan
 * Manages local crises, international politics, and IMF loan system
 * Requirements: 9.1, 9.2, 9.3, 9.4, 9.5
 */
class EventManager {
    constructor(gameEngine, uiManager) {
        this.gameEngine = gameEngine;
        this.uiManager = uiManager;
        this.activeEvents = [];
        this.eventHistory = [];
        this.lastEventTime = 0;
        this.eventCheckInterval = 30000; // Check for events every 30 seconds
        this.minTimeBetweenEvents = 60000; // Minimum 1 minute between events
        this.maxActiveEvents = 3; // Maximum concurrent events
        
        // Event probability modifiers
        this.eventProbabilityModifier = 1.0;
        this.crisisResistance = 1.0;
        
        // IMF loan state
        this.imfLoans = {
            active: [],
            totalDebt: 0,
            interestRate: 0.15,
            lastOfferTime: 0,
            offerCooldown: 120000 // 2 minutes between offers
        };
        
        // Initialize event configurations
        this.initializeEventConfigurations();
        
        console.log('EventManager: Initialized');
    }

    /**
     * Initialize event configurations
     */
    initializeEventConfigurations() {
        this.eventConfigs = {
            localCrisis: {
                drought: {
                    id: 'drought',
                    name: "Severe Drought",
                    description: "A devastating drought has struck Pooristan, threatening food production and economic stability.",
                    probability: 0.15,
                    duration: 45000, // 45 seconds
                    effects: {
                        resourceMultiplier: { food: 0.5 },
                        generatorMultiplier: { farm: 0.3 }
                    },
                    choices: [
                        {
                            id: 'emergency_import',
                            name: "Emergency Water Import",
                            description: "Import water and irrigation equipment to mitigate the drought",
                            cost: { gdp: 1000, industry: 50 },
                            effects: {
                                resourceMultiplier: { food: 0.8 },
                                generatorMultiplier: { farm: 0.7 }
                            }
                        },
                        {
                            id: 'do_nothing',
                            name: "Weather the Storm",
                            description: "Accept the drought's impact and hope for better conditions",
                            cost: {},
                            effects: {
                                resourceMultiplier: { food: 0.3 },
                                generatorMultiplier: { farm: 0.2 }
                            }
                        }
                    ]
                },
                
                civilUnrest: {
                    id: 'civilUnrest',
                    name: "Civil Unrest",
                    description: "Citizens are protesting economic conditions and government policies, disrupting productivity.",
                    probability: 0.12,
                    duration: 60000, // 60 seconds
                    effects: {
                        resourceMultiplier: { gdp: 0.7, influence: 0.5 },
                        generatorMultiplier: { factory: 0.6, school: 0.4 }
                    },
                    choices: [
                        {
                            id: 'social_programs',
                            name: "Increase Social Programs",
                            description: "Invest in social welfare to address citizen concerns",
                            cost: { gdp: 2000, food: 100 },
                            effects: {
                                resourceMultiplier: { gdp: 0.9, influence: 1.2 },
                                generatorMultiplier: { factory: 0.9, school: 0.8 }
                            }
                        },
                        {
                            id: 'suppress_protests',
                            name: "Suppress Protests",
                            description: "Use force to end protests quickly but damage international reputation",
                            cost: { industry: 100 },
                            effects: {
                                resourceMultiplier: { gdp: 0.8, influence: 0.3 },
                                generatorMultiplier: { factory: 0.8, school: 0.6 }
                            }
                        }
                    ]
                }
            },
            
            internationalPolitics: {
                tradeDeal: {
                    id: 'tradeDeal',
                    name: "International Trade Agreement",
                    description: "A foreign nation offers a trade partnership that could boost industry but may affect sovereignty.",
                    probability: 0.08,
                    duration: 0, // Permanent effect
                    choices: [
                        {
                            id: 'accept_deal',
                            name: "Accept Trade Deal",
                            description: "Boost industrial output but reduce political influence",
                            cost: {},
                            effects: {
                                resourceMultiplier: { industry: 1.5 },
                                generatorMultiplier: { factory: 1.3, embassy: 0.8 }
                            },
                            duration: 180000 // 3 minutes
                        },
                        {
                            id: 'reject_deal',
                            name: "Reject Trade Deal",
                            description: "Maintain sovereignty and gain influence points",
                            cost: {},
                            effects: {
                                resourceMultiplier: { influence: 1.2 },
                                generatorMultiplier: { embassy: 1.1 }
                            }
                        }
                    ]
                },
                
                diplomaticCrisis: {
                    id: 'diplomaticCrisis',
                    name: "Diplomatic Crisis",
                    description: "Tensions with neighboring countries threaten regional stability and trade relationships.",
                    probability: 0.06,
                    duration: 90000, // 90 seconds
                    effects: {
                        resourceMultiplier: { influence: 0.6, industry: 0.8 },
                        generatorMultiplier: { embassy: 0.5, factory: 0.7 }
                    },
                    choices: [
                        {
                            id: 'diplomatic_solution',
                            name: "Seek Diplomatic Solution",
                            description: "Invest in diplomacy to resolve the crisis peacefully",
                            cost: { influence: 20, knowledge: 50 },
                            effects: {
                                resourceMultiplier: { influence: 1.1, industry: 0.9 },
                                generatorMultiplier: { embassy: 1.2, factory: 0.9 }
                            }
                        },
                        {
                            id: 'military_posturing',
                            name: "Military Posturing",
                            description: "Show strength but risk escalating the situation",
                            cost: { industry: 200, gdp: 1500 },
                            effects: {
                                resourceMultiplier: { influence: 0.8, industry: 0.6 },
                                generatorMultiplier: { embassy: 0.7, factory: 0.5 }
                            }
                        }
                    ]
                }
            }
        };
    }

    /**
     * Check for events and potentially trigger new ones
     * Requirements: 9.1, 9.2, 9.3
     * @param {number} deltaTime - Time elapsed since last check
     */
    checkForEvents(deltaTime) {
        const currentTime = Date.now();
        
        // Update active event timers
        this.updateEventTimers(deltaTime);
        
        // Check if enough time has passed since last event
        if (currentTime - this.lastEventTime < this.minTimeBetweenEvents) {
            return;
        }
        
        // Don't trigger new events if we have too many active
        if (this.activeEvents.length >= this.maxActiveEvents) {
            return;
        }
        
        // Check for IMF loan triggers first
        this.checkIMFLoanTriggers();
        
        // Check for random events
        this.checkRandomEvents();
    }

    /**
     * Update timers for active events
     * @param {number} deltaTime - Time elapsed since last update
     */
    updateEventTimers(deltaTime) {
        const currentTime = Date.now();
        
        // Update active events and remove expired ones
        this.activeEvents = this.activeEvents.filter(event => {
            if (event.duration > 0 && currentTime - event.startTime > event.duration) {
                this.expireEvent(event);
                return false;
            }
            return true;
        });
        
        // Update IMF loan interest
        this.updateIMFLoans(deltaTime);
    }

    /**
     * Check for random event triggers
     */
    checkRandomEvents() {
        const gameState = this.gameEngine.getGameState();
        
        // Check local crisis events
        for (const [crisisType, config] of Object.entries(this.eventConfigs.localCrisis)) {
            if (this.shouldTriggerEvent(config)) {
                this.triggerLocalCrisis(crisisType, config);
                return; // Only trigger one event per check
            }
        }
        
        // Check international politics events
        for (const [eventType, config] of Object.entries(this.eventConfigs.internationalPolitics)) {
            if (this.shouldTriggerEvent(config)) {
                this.triggerInternationalEvent(eventType, config);
                return; // Only trigger one event per check
            }
        }
    }

    /**
     * Determine if an event should be triggered based on probability
     * @param {Object} config - Event configuration
     * @returns {boolean} - Whether to trigger the event
     */
    shouldTriggerEvent(config) {
        const baseProbability = config.probability * this.eventProbabilityModifier;
        const adjustedProbability = baseProbability * this.crisisResistance;
        return Math.random() < adjustedProbability;
    }

    /**
     * Trigger a local crisis event
     * Requirements: 9.2
     * @param {string} crisisType - Type of crisis
     * @param {Object} config - Crisis configuration
     */
    triggerLocalCrisis(crisisType, config) {
        const event = this.createEvent('localCrisis', crisisType, config);
        this.activateEvent(event);
        console.log(`EventManager: Triggered local crisis - ${config.name}`);
    }

    /**
     * Trigger an international politics event
     * Requirements: 9.3
     * @param {string} eventType - Type of international event
     * @param {Object} config - Event configuration
     */
    triggerInternationalEvent(eventType, config) {
        const event = this.createEvent('internationalPolitics', eventType, config);
        this.activateEvent(event);
        console.log(`EventManager: Triggered international event - ${config.name}`);
    }

    /**
     * Check for IMF loan trigger conditions
     * Requirements: 9.4
     */
    checkIMFLoanTriggers() {
        const currentTime = Date.now();
        const gameState = this.gameEngine.getGameState();

        // Check cooldown
        if (currentTime - this.imfLoans.lastOfferTime < this.imfLoans.offerCooldown) {
            return;
        }

        // Check if player is in financial trouble
        const shouldOfferLoan = (
            gameState.gdp < 500 || // Low GDP
            gameState.resources.food < 10 || // Food shortage
            (gameState.resources.industry < 50 && gameState.generators.factory.count > 5) // Industrial crisis
        );

        if (shouldOfferLoan && Math.random() < 0.3) { // 30% chance when conditions are met
            this.triggerIMFLoan();
        }
    }

    /**
     * Trigger an IMF loan offer
     * Requirements: 9.4
     */
    triggerIMFLoan() {
        const gameState = this.gameEngine.getGameState();
        const loanAmount = Math.max(1000, Math.floor(gameState.gdp * 2)); // Loan based on current GDP

        const imfLoanConfig = {
            id: 'imfLoan',
            name: "IMF Emergency Loan Offer",
            description: `The International Monetary Fund offers an emergency loan of ${NumberFormatter.format(loanAmount)} GDP to help stabilize your economy. However, accepting comes with strict austerity measures that will reduce your resource generation.`,
            probability: 1.0, // Always trigger when called
            duration: 0, // Permanent until repaid
            loanAmount: loanAmount,
            interestRate: this.imfLoans.interestRate,
            choices: [
                {
                    id: 'accept_loan',
                    name: "Accept IMF Loan",
                    description: `Receive ${NumberFormatter.format(loanAmount)} GDP immediately but accept austerity measures`,
                    cost: {},
                    effects: {
                        gdpGain: loanAmount,
                        resourceMultiplier: { food: 0.8, industry: 0.7, knowledge: 0.6 },
                        generatorMultiplier: { farm: 0.8, factory: 0.7, school: 0.6 }
                    },
                    loanData: {
                        amount: loanAmount,
                        interestRate: this.imfLoans.interestRate,
                        startTime: Date.now()
                    }
                },
                {
                    id: 'reject_loan',
                    name: "Reject IMF Loan",
                    description: "Maintain economic independence but continue struggling",
                    cost: {},
                    effects: {
                        resourceMultiplier: { influence: 1.1 }
                    }
                }
            ]
        };

        const event = this.createEvent('imfLoan', 'emergencyLoan', imfLoanConfig);
        this.activateEvent(event);
        this.imfLoans.lastOfferTime = Date.now();
        console.log('EventManager: Triggered IMF loan offer');
    }

    /**
     * Create a new event object
     * @param {string} category - Event category
     * @param {string} type - Event type
     * @param {Object} config - Event configuration
     * @returns {Object} - Created event
     */
    createEvent(category, type, config) {
        const eventId = `${category}_${type}_${Date.now()}`;
        return {
            id: eventId,
            category: category,
            type: type,
            name: config.name,
            description: config.description,
            duration: config.duration || 0,
            effects: config.effects || {},
            choices: config.choices || [],
            isActive: false,
            startTime: 0,
            loanAmount: config.loanAmount || 0,
            interestRate: config.interestRate || 0
        };
    }

    /**
     * Activate an event and show it to the player
     * Requirements: 9.1
     * @param {Object} event - Event to activate
     */
    activateEvent(event) {
        event.isActive = true;
        event.startTime = Date.now();

        this.activeEvents.push(event);
        this.lastEventTime = Date.now();

        // Apply immediate effects if any
        if (event.effects) {
            this.applyEventEffects(event.effects);
        }

        // Show event modal to player
        this.uiManager.showEventModal(event);

        // Add to history
        this.eventHistory.push({
            id: event.id,
            name: event.name,
            timestamp: event.startTime,
            resolved: false
        });
    }

    /**
     * Resolve an event with player's choice
     * Requirements: 9.5
     * @param {string} eventId - ID of the event to resolve
     * @param {string} choiceId - ID of the chosen option
     */
    resolveEvent(eventId, choiceId) {
        const event = this.activeEvents.find(e => e.id === eventId);
        if (!event) {
            console.error(`EventManager: Event ${eventId} not found`);
            return;
        }

        const choice = event.choices.find(c => c.id === choiceId);
        if (!choice) {
            console.error(`EventManager: Choice ${choiceId} not found for event ${eventId}`);
            return;
        }

        console.log(`EventManager: Resolving event ${event.name} with choice ${choice.name}`);

        // Check if player can afford the choice
        if (!this.canAffordChoice(choice)) {
            console.warn('EventManager: Player cannot afford this choice');
            this.uiManager.showNotification('Insufficient resources for this choice!', 'error');
            return;
        }

        // Apply choice costs
        this.applyChoiceCosts(choice);

        // Apply choice effects
        this.applyChoiceEffects(choice, event);

        // Handle IMF loan acceptance
        if (event.category === 'imfLoan' && choiceId === 'accept_loan') {
            this.acceptIMFLoan(choice.loanData);
        }

        // Remove event from active events
        this.activeEvents = this.activeEvents.filter(e => e.id !== eventId);

        // Update event history
        const historyEntry = this.eventHistory.find(h => h.id === eventId);
        if (historyEntry) {
            historyEntry.resolved = true;
            historyEntry.choice = choice.name;
            historyEntry.resolvedTime = Date.now();
        }

        // Hide event modal
        this.uiManager.hideEventModal();

        // Update UI displays
        this.uiManager.updateAllDisplays();
    }

    /**
     * Check if player can afford a choice
     * @param {Object} choice - Choice to check
     * @returns {boolean} - Whether player can afford the choice
     */
    canAffordChoice(choice) {
        if (!choice.cost) return true;

        const gameState = this.gameEngine.getGameState();

        for (const [resource, amount] of Object.entries(choice.cost)) {
            if (resource === 'gdp') {
                if (gameState.gdp < amount) return false;
            } else if (gameState.resources[resource] < amount) {
                return false;
            }
        }

        return true;
    }

    /**
     * Apply costs of a choice to game state
     * @param {Object} choice - Choice with costs to apply
     */
    applyChoiceCosts(choice) {
        if (!choice.cost) return;

        const gameState = this.gameEngine.getGameState();

        for (const [resource, amount] of Object.entries(choice.cost)) {
            if (resource === 'gdp') {
                gameState.gdp = Math.max(0, gameState.gdp - amount);
            } else if (gameState.resources[resource] !== undefined) {
                gameState.resources[resource] = Math.max(0, gameState.resources[resource] - amount);
            }
        }

        console.log('EventManager: Applied choice costs', choice.cost);
    }

    /**
     * Apply effects of a choice to game state
     * @param {Object} choice - Choice with effects to apply
     * @param {Object} event - Original event
     */
    applyChoiceEffects(choice, event) {
        if (!choice.effects) return;

        // Apply immediate GDP gain
        if (choice.effects.gdpGain) {
            const gameState = this.gameEngine.getGameState();
            gameState.gdp += choice.effects.gdpGain;
        }

        // Apply resource and generator multipliers
        this.applyEventEffects(choice.effects, choice.duration || event.duration);

        console.log('EventManager: Applied choice effects', choice.effects);
    }

    /**
     * Apply event effects to game state
     * @param {Object} effects - Effects to apply
     * @param {number} duration - Duration of effects (0 for permanent)
     */
    applyEventEffects(effects, duration = 0) {
        if (!effects) return;

        const gameState = this.gameEngine.getGameState();

        // Store effects in game state for the game engine to apply
        if (!gameState.events) {
            gameState.events = {
                activeEffects: [],
                eventHistory: []
            };
        }

        const effectEntry = {
            id: `effect_${Date.now()}`,
            effects: effects,
            startTime: Date.now(),
            duration: duration,
            isActive: true
        };

        gameState.events.activeEffects.push(effectEntry);

        console.log('EventManager: Applied event effects', effects);
    }

    /**
     * Accept an IMF loan
     * Requirements: 9.4
     * @param {Object} loanData - Loan details
     */
    acceptIMFLoan(loanData) {
        const loan = {
            id: `imf_loan_${Date.now()}`,
            amount: loanData.amount,
            interestRate: loanData.interestRate,
            startTime: loanData.startTime,
            monthlyPayment: Math.floor(loanData.amount * 0.1), // 10% of loan amount per month
            remainingBalance: loanData.amount
        };

        this.imfLoans.active.push(loan);
        this.imfLoans.totalDebt += loanData.amount;

        // Add GDP immediately
        const gameState = this.gameEngine.getGameState();
        gameState.gdp += loanData.amount;

        console.log(`EventManager: Accepted IMF loan of ${loanData.amount} GDP`);
    }

    /**
     * Update IMF loans (interest and payments)
     * @param {number} deltaTime - Time elapsed since last update
     */
    updateIMFLoans(deltaTime) {
        const currentTime = Date.now();
        const gameState = this.gameEngine.getGameState();

        this.imfLoans.active.forEach(loan => {
            // Calculate interest (compound monthly)
            const monthsElapsed = (currentTime - loan.startTime) / (30 * 24 * 60 * 60 * 1000); // 30 days per month
            const interestAccrued = loan.amount * Math.pow(1 + loan.interestRate / 12, monthsElapsed) - loan.amount;

            // Deduct automatic payments from GDP
            const timeSinceLastPayment = currentTime - (loan.lastPayment || loan.startTime);
            if (timeSinceLastPayment > 60000) { // Payment every minute (accelerated time)
                const payment = Math.min(loan.monthlyPayment, gameState.gdp);
                gameState.gdp = Math.max(0, gameState.gdp - payment);
                loan.remainingBalance = Math.max(0, loan.remainingBalance - payment + interestAccrued);
                loan.lastPayment = currentTime;

                if (loan.remainingBalance <= 0) {
                    console.log('EventManager: IMF loan fully repaid');
                }
            }
        });

        // Remove fully repaid loans
        this.imfLoans.active = this.imfLoans.active.filter(loan => loan.remainingBalance > 0);

        // Update total debt
        this.imfLoans.totalDebt = this.imfLoans.active.reduce((total, loan) => total + loan.remainingBalance, 0);
    }

    /**
     * Expire an event when its duration ends
     * @param {Object} event - Event to expire
     */
    expireEvent(event) {
        console.log(`EventManager: Event ${event.name} has expired`);

        // Remove effects from game state
        const gameState = this.gameEngine.getGameState();
        if (gameState.events && gameState.events.activeEffects) {
            gameState.events.activeEffects = gameState.events.activeEffects.filter(effect =>
                effect.startTime !== event.startTime
            );
        }

        // Update event history
        const historyEntry = this.eventHistory.find(h => h.id === event.id);
        if (historyEntry) {
            historyEntry.expired = true;
            historyEntry.expiredTime = Date.now();
        }
    }

    /**
     * Get all active events
     * @returns {Array} - Array of active events
     */
    getActiveEvents() {
        return [...this.activeEvents];
    }

    /**
     * Get event history
     * @returns {Array} - Array of historical events
     */
    getEventHistory() {
        return [...this.eventHistory];
    }

    /**
     * Get IMF loan status
     * @returns {Object} - IMF loan information
     */
    getIMFLoanStatus() {
        return {
            totalDebt: this.imfLoans.totalDebt,
            activeLoans: this.imfLoans.active.length,
            interestRate: this.imfLoans.interestRate,
            loans: [...this.imfLoans.active]
        };
    }

    /**
     * Set event probability modifier (for difficulty scaling)
     * @param {number} modifier - Probability modifier (1.0 = normal)
     */
    setEventProbabilityModifier(modifier) {
        this.eventProbabilityModifier = Math.max(0, modifier);
        console.log(`EventManager: Event probability modifier set to ${modifier}`);
    }

    /**
     * Set crisis resistance (for technology effects)
     * @param {number} resistance - Crisis resistance (1.0 = normal)
     */
    setCrisisResistance(resistance) {
        this.crisisResistance = Math.max(0.1, resistance);
        console.log(`EventManager: Crisis resistance set to ${resistance}`);
    }

    /**
     * Get current event system state for saving
     * @returns {Object} - Event system state
     */
    getEventSystemState() {
        return {
            activeEvents: this.activeEvents,
            eventHistory: this.eventHistory,
            lastEventTime: this.lastEventTime,
            imfLoans: this.imfLoans,
            eventProbabilityModifier: this.eventProbabilityModifier,
            crisisResistance: this.crisisResistance
        };
    }

    /**
     * Load event system state from save data
     * @param {Object} state - Saved event system state
     */
    loadEventSystemState(state) {
        if (!state) return;

        this.activeEvents = state.activeEvents || [];
        this.eventHistory = state.eventHistory || [];
        this.lastEventTime = state.lastEventTime || 0;
        this.imfLoans = state.imfLoans || {
            active: [],
            totalDebt: 0,
            interestRate: 0.15,
            lastOfferTime: 0,
            offerCooldown: 120000
        };
        this.eventProbabilityModifier = state.eventProbabilityModifier || 1.0;
        this.crisisResistance = state.crisisResistance || 1.0;

        console.log('EventManager: Loaded event system state');
    }
}
