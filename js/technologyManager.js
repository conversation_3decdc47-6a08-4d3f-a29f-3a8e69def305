/**
 * TechnologyManager - Manages the technology research system and unlocks
 * Handles technology research progression, validation, and effects application
 * Requirements: 8.1, 8.2, 8.3, 8.4, 8.5
 */

class TechnologyManager {
    constructor(gameEngine) {
        this.gameEngine = gameEngine;
        this.technologyTree = this.initializeTechnologyTree();
        this.researchQueue = [];
        this.currentResearch = null;
        this.researchStartTime = 0;
        this.researchDuration = 10000; // 10 seconds base research time

        // Initialize available technologies
        this.updateAvailableTechnologies();

        console.log('TechnologyManager: Initialized');
    }

    /**
     * Initialize the complete technology tree configuration
     * Based on design.md specifications
     */
    initializeTechnologyTree() {
        return {
            // Tier 1 - Basic Technologies
            basicAgriculture: {
                id: 'basicAgriculture',
                name: "Basic Agriculture",
                description: "Improves farm efficiency by 25%",
                cost: { knowledge: 10 },
                researchTime: 5000, // 5 seconds
                prerequisites: [],
                effects: {
                    generatorMultiplier: { farm: 1.25 }
                },
                unlocks: ['irrigation', 'animalHusbandry'],
                tier: 1,
                position: { x: 100, y: 100 }
            },

            industrialization: {
                id: 'industrialization',
                name: "Industrialization",
                description: "Unlocks factory generators for industrial production",
                cost: { knowledge: 25 },
                researchTime: 8000, // 8 seconds
                prerequisites: [],
                effects: {
                    unlockGenerator: 'factory'
                },
                unlocks: ['massProduction', 'automation'],
                tier: 1,
                position: { x: 300, y: 100 }
            },

            education: {
                id: 'education',
                name: "Public Education",
                description: "Unlocks school generators for knowledge production",
                cost: { food: 100, industry: 50 },
                researchTime: 12000, // 12 seconds
                prerequisites: [],
                effects: {
                    unlockGenerator: 'school'
                },
                unlocks: ['higherEducation', 'research'],
                tier: 1,
                position: { x: 500, y: 100 }
            },

            // Tier 2 - Advanced Technologies
            irrigation: {
                id: 'irrigation',
                name: "Irrigation Systems",
                description: "Reduces farm costs by 10% and increases production by 50%",
                cost: { knowledge: 50, industry: 20 },
                researchTime: 15000, // 15 seconds
                prerequisites: ['basicAgriculture'],
                effects: {
                    generatorMultiplier: { farm: 1.5 },
                    costReduction: { farm: 0.9 }
                },
                unlocks: ['modernAgriculture'],
                tier: 2,
                position: { x: 100, y: 200 }
            },

            animalHusbandry: {
                id: 'animalHusbandry',
                name: "Animal Husbandry",
                description: "Increases farm production by 75% through livestock",
                cost: { knowledge: 75, food: 50 },
                researchTime: 18000, // 18 seconds
                prerequisites: ['basicAgriculture'],
                effects: {
                    generatorMultiplier: { farm: 1.75 }
                },
                unlocks: ['modernAgriculture'],
                tier: 2,
                position: { x: 50, y: 250 }
            },

            massProduction: {
                id: 'massProduction',
                name: "Mass Production",
                description: "Increases factory efficiency by 100%",
                cost: { knowledge: 100, industry: 75 },
                researchTime: 20000, // 20 seconds
                prerequisites: ['industrialization'],
                effects: {
                    generatorMultiplier: { factory: 2.0 }
                },
                unlocks: ['automation'],
                tier: 2,
                position: { x: 300, y: 200 }
            },

            automation: {
                id: 'automation',
                name: "Industrial Automation",
                description: "Reduces factory costs by 15% and increases production by 50%",
                cost: { knowledge: 150, industry: 100 },
                researchTime: 25000, // 25 seconds
                prerequisites: ['industrialization', 'massProduction'],
                effects: {
                    generatorMultiplier: { factory: 1.5 },
                    costReduction: { factory: 0.85 }
                },
                unlocks: ['cryptocurrency'],
                tier: 2,
                position: { x: 350, y: 250 }
            },

            higherEducation: {
                id: 'higherEducation',
                name: "Higher Education",
                description: "Increases school efficiency by 100%",
                cost: { knowledge: 200, industry: 100 },
                researchTime: 22000, // 22 seconds
                prerequisites: ['education'],
                effects: {
                    generatorMultiplier: { school: 2.0 }
                },
                unlocks: ['research'],
                tier: 2,
                position: { x: 500, y: 200 }
            },

            research: {
                id: 'research',
                name: "Research & Development",
                description: "Reduces all research times by 25% and increases school production by 50%",
                cost: { knowledge: 300, industry: 150 },
                researchTime: 30000, // 30 seconds
                prerequisites: ['education', 'higherEducation'],
                effects: {
                    generatorMultiplier: { school: 1.5 },
                    researchTimeReduction: 0.75 // 25% faster research
                },
                unlocks: ['cryptocurrency'],
                tier: 2,
                position: { x: 550, y: 250 }
            },

            // Tier 3 - Specialized Technologies
            modernAgriculture: {
                id: 'modernAgriculture',
                name: "Modern Agriculture",
                description: "Advanced farming techniques increase production by 200%",
                cost: { knowledge: 500, industry: 300, food: 200 },
                researchTime: 35000, // 35 seconds
                prerequisites: ['irrigation', 'animalHusbandry'],
                effects: {
                    generatorMultiplier: { farm: 3.0 }
                },
                unlocks: [],
                tier: 3,
                position: { x: 100, y: 350 }
            },

            diplomacy: {
                id: 'diplomacy',
                name: "Diplomatic Relations",
                description: "Unlocks embassy generators for influence production",
                cost: { knowledge: 100, industry: 100 },
                researchTime: 25000, // 25 seconds
                prerequisites: ['education'],
                effects: {
                    unlockGenerator: 'embassy'
                },
                unlocks: ['internationalTrade', 'globalInfluence'],
                tier: 3,
                position: { x: 700, y: 200 }
            },

            internationalTrade: {
                id: 'internationalTrade',
                name: "International Trade",
                description: "Increases all resource generation by 25%",
                cost: { knowledge: 300, industry: 200, influence: 50 },
                researchTime: 40000, // 40 seconds
                prerequisites: ['diplomacy'],
                effects: {
                    globalMultiplier: 1.25
                },
                unlocks: ['globalInfluence'],
                tier: 3,
                position: { x: 700, y: 300 }
            },

            globalInfluence: {
                id: 'globalInfluence',
                name: "Global Influence",
                description: "Increases embassy efficiency by 300% and unlocks victory path",
                cost: { knowledge: 1000, industry: 500, influence: 100 },
                researchTime: 50000, // 50 seconds
                prerequisites: ['diplomacy', 'internationalTrade'],
                effects: {
                    generatorMultiplier: { embassy: 4.0 },
                    victoryBonus: true
                },
                unlocks: ['aiDevelopment'],
                tier: 3,
                position: { x: 750, y: 400 }
            },

            // Tier 4 - Advanced Technologies
            cryptocurrency: {
                id: 'cryptocurrency',
                name: "Cryptocurrency Adoption",
                description: "Unlocks Bitcoin mining operations for digital currency",
                cost: { knowledge: 500, industry: 1000 },
                researchTime: 45000, // 45 seconds
                prerequisites: ['automation', 'research'],
                effects: {
                    unlockGenerator: 'bitcoinMiner'
                },
                unlocks: ['aiDevelopment'],
                tier: 4,
                position: { x: 400, y: 350 }
            },

            // Tier 5 - Ultimate Technology
            aiDevelopment: {
                id: 'aiDevelopment',
                name: "AI Machine God Project",
                description: "Ultimate technology for universal dominance - Victory condition!",
                cost: { knowledge: 10000, industry: 50000, bitcoin: 1000 },
                researchTime: 120000, // 2 minutes
                prerequisites: ['cryptocurrency', 'globalInfluence'],
                effects: {
                    victoryCondition: true,
                    influenceMultiplier: 10,
                    globalMultiplier: 5.0
                },
                unlocks: [],
                tier: 5,
                position: { x: 500, y: 500 }
            }
        };
    }

    /**
     * Start research on a technology
     * Requirements: 8.1, 8.2
     */
    startResearch(technologyId) {
        if (!this.canResearch(technologyId)) {
            console.warn(`Cannot research technology: ${technologyId}`);
            return false;
        }

        const technology = this.technologyTree[technologyId];
        const gameState = this.gameEngine.getGameState();

        // Check if already researched
        if (gameState.technologies.researched.includes(technologyId)) {
            console.warn(`Technology already researched: ${technologyId}`);
            return false;
        }

        // Check if already in progress
        if (gameState.technologies.inProgress === technologyId) {
            console.warn(`Technology already in progress: ${technologyId}`);
            return false;
        }

        // Deduct research costs
        if (!this.deductResearchCosts(technology.cost)) {
            console.warn(`Cannot afford research costs for: ${technologyId}`);
            return false;
        }

        // Start research
        gameState.technologies.inProgress = technologyId;
        gameState.technologies.researchProgress = 0;
        this.currentResearch = technologyId;
        this.researchStartTime = Date.now();

        // Apply research time reduction if available
        const timeReduction = this.getTechnologyMultipliers().researchTimeReduction || 1.0;
        this.researchDuration = technology.researchTime * timeReduction;

        console.log(`Started research on: ${technology.name} (${this.researchDuration}ms)`);
        return true;
    }

    /**
     * Update research progress
     * Requirements: 8.1
     */
    updateResearchProgress(deltaTime) {
        const gameState = this.gameEngine.getGameState();

        if (!gameState.technologies.inProgress) {
            return;
        }

        const currentTime = Date.now();
        const elapsedTime = currentTime - this.researchStartTime;
        const progress = Math.min(elapsedTime / this.researchDuration, 1.0);

        gameState.technologies.researchProgress = progress;

        // Complete research if finished
        if (progress >= 1.0) {
            this.completeResearch(gameState.technologies.inProgress);
        }
    }

    /**
     * Complete research and apply effects
     * Requirements: 8.1, 8.4, 8.5
     */
    completeResearch(technologyId) {
        const gameState = this.gameEngine.getGameState();
        const technology = this.technologyTree[technologyId];

        if (!technology) {
            console.error(`Technology not found: ${technologyId}`);
            return false;
        }

        // Mark as researched
        gameState.technologies.researched.push(technologyId);
        gameState.technologies.inProgress = null;
        gameState.technologies.researchProgress = 0;
        this.currentResearch = null;

        // Apply technology effects
        this.applyTechnologyEffects(technologyId);

        // Update available technologies
        this.updateAvailableTechnologies();

        console.log(`Research completed: ${technology.name}`);

        // Notify UI if available
        if (this.gameEngine.uiManager) {
            this.gameEngine.uiManager.showNotification(
                `Research Complete: ${technology.name}`,
                'success'
            );
        }

        return true;
    }

    /**
     * Check if a technology can be researched
     * Requirements: 8.2, 8.3
     */
    canResearch(technologyId) {
        const gameState = this.gameEngine.getGameState();
        const technology = this.technologyTree[technologyId];

        if (!technology) {
            return false;
        }

        // Check if already researched
        if (gameState.technologies.researched.includes(technologyId)) {
            return false;
        }

        // Check if research is in progress
        if (gameState.technologies.inProgress) {
            return false;
        }

        // Check prerequisites
        if (!this.checkPrerequisites(technologyId)) {
            return false;
        }

        // Check if player can afford the cost
        if (!this.canAffordResearch(technology.cost)) {
            return false;
        }

        return true;
    }

    /**
     * Check if prerequisites are met
     * Requirements: 8.2, 8.3
     */
    checkPrerequisites(technologyId) {
        const technology = this.technologyTree[technologyId];
        const gameState = this.gameEngine.getGameState();

        if (!technology.prerequisites || technology.prerequisites.length === 0) {
            return true;
        }

        return technology.prerequisites.every(prereqId =>
            gameState.technologies.researched.includes(prereqId)
        );
    }

    /**
     * Check if player can afford research costs
     * Requirements: 8.1, 8.2
     */
    canAffordResearch(costs) {
        const gameState = this.gameEngine.getGameState();

        for (const [resourceType, amount] of Object.entries(costs)) {
            if (resourceType === 'gdp') {
                if (gameState.gdp < amount) {
                    return false;
                }
            } else if (gameState.resources[resourceType] < amount) {
                return false;
            }
        }

        return true;
    }

    /**
     * Deduct research costs from player resources
     * Requirements: 8.1
     */
    deductResearchCosts(costs) {
        if (!this.canAffordResearch(costs)) {
            return false;
        }

        const gameState = this.gameEngine.getGameState();

        for (const [resourceType, amount] of Object.entries(costs)) {
            if (resourceType === 'gdp') {
                gameState.gdp -= amount;
            } else {
                gameState.resources[resourceType] -= amount;
            }
        }

        return true;
    }

    /**
     * Apply technology effects to game state
     * Requirements: 8.4, 8.5
     */
    applyTechnologyEffects(technologyId) {
        const technology = this.technologyTree[technologyId];
        const gameState = this.gameEngine.getGameState();

        if (!technology.effects) {
            return;
        }

        // Apply generator multipliers
        if (technology.effects.generatorMultiplier) {
            for (const [generatorType, multiplier] of Object.entries(technology.effects.generatorMultiplier)) {
                if (gameState.generators[generatorType]) {
                    if (!gameState.generators[generatorType].technologyMultipliers) {
                        gameState.generators[generatorType].technologyMultipliers = {};
                    }
                    gameState.generators[generatorType].technologyMultipliers[technologyId] = multiplier;
                }
            }
        }

        // Apply cost reductions
        if (technology.effects.costReduction) {
            for (const [generatorType, reduction] of Object.entries(technology.effects.costReduction)) {
                if (gameState.generators[generatorType]) {
                    if (!gameState.generators[generatorType].costReductions) {
                        gameState.generators[generatorType].costReductions = {};
                    }
                    gameState.generators[generatorType].costReductions[technologyId] = reduction;
                }
            }
        }

        // Unlock generators
        if (technology.effects.unlockGenerator) {
            const generatorType = technology.effects.unlockGenerator;
            if (gameState.generators[generatorType]) {
                gameState.generators[generatorType].unlocked = true;
                console.log(`Unlocked generator: ${generatorType}`);
            }
        }

        // Apply global multipliers
        if (technology.effects.globalMultiplier) {
            if (!gameState.globalMultipliers) {
                gameState.globalMultipliers = {};
            }
            gameState.globalMultipliers[technologyId] = technology.effects.globalMultiplier;
        }

        // Apply influence multipliers
        if (technology.effects.influenceMultiplier) {
            if (!gameState.influenceMultipliers) {
                gameState.influenceMultipliers = {};
            }
            gameState.influenceMultipliers[technologyId] = technology.effects.influenceMultiplier;
        }

        // Apply research time reduction
        if (technology.effects.researchTimeReduction) {
            if (!gameState.researchTimeReductions) {
                gameState.researchTimeReductions = {};
            }
            gameState.researchTimeReductions[technologyId] = technology.effects.researchTimeReduction;
        }

        // Check victory conditions
        if (technology.effects.victoryCondition) {
            gameState.victory.gameWon = true;
            console.log('Victory condition achieved through technology!');

            // Notify game engine of victory
            if (this.gameEngine.uiManager) {
                this.gameEngine.uiManager.showNotification(
                    'VICTORY! You have achieved technological dominance!',
                    'victory'
                );
            }
        }

        // Recalculate production rates with new multipliers
        this.gameEngine.recalculateProductionRates();
    }

    /**
     * Get available technologies for research
     * Requirements: 8.2, 8.3
     */
    getAvailableTechnologies() {
        const gameState = this.gameEngine.getGameState();
        const available = [];

        for (const [techId, technology] of Object.entries(this.technologyTree)) {
            // Skip if already researched
            if (gameState.technologies.researched.includes(techId)) {
                continue;
            }

            // Check if prerequisites are met
            if (this.checkPrerequisites(techId)) {
                available.push({
                    id: techId,
                    ...technology,
                    canAfford: this.canAffordResearch(technology.cost),
                    canResearch: this.canResearch(techId)
                });
            }
        }

        return available.sort((a, b) => a.tier - b.tier);
    }

    /**
     * Update available technologies based on current research state
     * Requirements: 8.2, 8.3
     */
    updateAvailableTechnologies() {
        const gameState = this.gameEngine.getGameState();
        const available = [];

        // Find all technologies that have their prerequisites met
        for (const [techId, technology] of Object.entries(this.technologyTree)) {
            if (!gameState.technologies.researched.includes(techId) &&
                this.checkPrerequisites(techId)) {
                available.push(techId);
            }
        }

        gameState.technologies.available = available;
    }

    /**
     * Get technology cost for display
     * Requirements: 8.1, 8.2
     */
    getTechnologyCost(technologyId) {
        const technology = this.technologyTree[technologyId];
        return technology ? technology.cost : null;
    }

    /**
     * Get current technology multipliers
     * Requirements: 8.4, 8.5
     */
    getTechnologyMultipliers() {
        const gameState = this.gameEngine.getGameState();
        const multipliers = {
            generators: {},
            global: 1.0,
            influence: 1.0,
            researchTimeReduction: 1.0
        };

        // Calculate generator multipliers
        for (const [generatorType, generator] of Object.entries(gameState.generators)) {
            multipliers.generators[generatorType] = 1.0;

            if (generator.technologyMultipliers) {
                for (const multiplier of Object.values(generator.technologyMultipliers)) {
                    multipliers.generators[generatorType] *= multiplier;
                }
            }
        }

        // Calculate global multiplier
        if (gameState.globalMultipliers) {
            for (const multiplier of Object.values(gameState.globalMultipliers)) {
                multipliers.global *= multiplier;
            }
        }

        // Calculate influence multiplier
        if (gameState.influenceMultipliers) {
            for (const multiplier of Object.values(gameState.influenceMultipliers)) {
                multipliers.influence *= multiplier;
            }
        }

        // Calculate research time reduction
        if (gameState.researchTimeReductions) {
            for (const reduction of Object.values(gameState.researchTimeReductions)) {
                multipliers.researchTimeReduction *= reduction;
            }
        }

        return multipliers;
    }

    /**
     * Get researched technologies
     * Requirements: 8.1
     */
    getResearchedTechnologies() {
        const gameState = this.gameEngine.getGameState();
        return gameState.technologies.researched.map(techId => ({
            id: techId,
            ...this.technologyTree[techId]
        }));
    }

    /**
     * Get current research status
     * Requirements: 8.1
     */
    getCurrentResearchStatus() {
        const gameState = this.gameEngine.getGameState();

        if (!gameState.technologies.inProgress) {
            return null;
        }

        const technology = this.technologyTree[gameState.technologies.inProgress];
        return {
            id: gameState.technologies.inProgress,
            name: technology.name,
            progress: gameState.technologies.researchProgress,
            timeRemaining: this.researchDuration * (1 - gameState.technologies.researchProgress)
        };
    }

    /**
     * Cancel current research (for future use)
     * Requirements: 8.1
     */
    cancelResearch() {
        const gameState = this.gameEngine.getGameState();

        if (gameState.technologies.inProgress) {
            console.log(`Cancelled research: ${gameState.technologies.inProgress}`);
            gameState.technologies.inProgress = null;
            gameState.technologies.researchProgress = 0;
            this.currentResearch = null;
            return true;
        }

        return false;
    }

    /**
     * Get technology tree for UI display
     * Requirements: 8.3
     */
    getTechnologyTreeForDisplay() {
        const gameState = this.gameEngine.getGameState();
        const treeData = {};

        for (const [techId, technology] of Object.entries(this.technologyTree)) {
            treeData[techId] = {
                ...technology,
                isResearched: gameState.technologies.researched.includes(techId),
                isAvailable: gameState.technologies.available.includes(techId),
                isInProgress: gameState.technologies.inProgress === techId,
                canAfford: this.canAffordResearch(technology.cost),
                canResearch: this.canResearch(techId),
                progress: gameState.technologies.inProgress === techId ?
                         gameState.technologies.researchProgress : 0
            };
        }

        return treeData;
    }
}
