/**
 * AudioManager - Manages sound effects and audio feedback for Pooristan
 * Handles audio playback, volume control, and sound caching
 * Requirements: 12.1, 12.3, 12.5
 */
class AudioManager {
    constructor() {
        this.isEnabled = true;
        this.volume = 0.7; // Default volume (0.0 to 1.0)
        this.isMuted = false;
        this.sounds = new Map();
        this.audioContext = null;
        this.masterGainNode = null;
        
        // Sound file paths
        this.soundPaths = {
            click: 'assets/audio/click.mp3',
            purchase: 'assets/audio/purchase.mp3',
            upgrade: 'assets/audio/upgrade.mp3',
            event: 'assets/audio/event.mp3',
            victory: 'assets/audio/victory.mp3',
            notification: 'assets/audio/notification.mp3',
            error: 'assets/audio/error.mp3',
            milestone: 'assets/audio/milestone.mp3'
        };
        
        // Initialize audio system
        this.initialize();
    }

    /**
     * Initialize audio system
     * Requirements: 12.1
     */
    async initialize() {
        try {
            // Create audio context for better control
            if (typeof AudioContext !== 'undefined') {
                this.audioContext = new AudioContext();
                this.masterGainNode = this.audioContext.createGain();
                this.masterGainNode.connect(this.audioContext.destination);
                this.masterGainNode.gain.value = this.volume;
            }
            
            // Preload all sounds
            await this.preloadSounds();
            
            console.log('AudioManager: Initialized successfully');
        } catch (error) {
            console.warn('AudioManager: Failed to initialize audio system:', error);
            this.isEnabled = false;
        }
    }

    /**
     * Preload all sound effects
     * Requirements: 12.1
     */
    async preloadSounds() {
        const loadPromises = [];
        
        for (const [soundName, soundPath] of Object.entries(this.soundPaths)) {
            loadPromises.push(this.loadSound(soundName, soundPath));
        }
        
        try {
            await Promise.all(loadPromises);
            console.log('AudioManager: All sounds preloaded successfully');
        } catch (error) {
            console.warn('AudioManager: Some sounds failed to load:', error);
        }
    }

    /**
     * Load a single sound file
     * @param {string} name - Sound identifier
     * @param {string} path - Path to sound file
     */
    async loadSound(name, path) {
        try {
            // Create audio element for fallback
            const audio = new Audio();
            audio.preload = 'auto';
            audio.volume = this.volume;
            
            // Create promise to handle loading
            const loadPromise = new Promise((resolve, reject) => {
                audio.addEventListener('canplaythrough', resolve);
                audio.addEventListener('error', reject);
                audio.src = path;
            });
            
            await loadPromise;
            this.sounds.set(name, audio);
            
        } catch (error) {
            // Create silent fallback for missing audio files
            console.warn(`AudioManager: Failed to load sound '${name}' from '${path}':`, error);
            this.sounds.set(name, null);
        }
    }

    /**
     * Play a sound effect
     * @param {string} soundName - Name of the sound to play
     * @param {number} volumeMultiplier - Volume multiplier for this specific sound
     */
    playSound(soundName, volumeMultiplier = 1.0) {
        if (!this.isEnabled || this.isMuted) return;
        
        const sound = this.sounds.get(soundName);
        if (!sound) {
            console.warn(`AudioManager: Sound '${soundName}' not found`);
            return;
        }
        
        try {
            // Clone the audio for overlapping sounds
            const audioClone = sound.cloneNode();
            audioClone.volume = Math.min(1.0, this.volume * volumeMultiplier);
            
            // Play the sound
            const playPromise = audioClone.play();
            if (playPromise !== undefined) {
                playPromise.catch(error => {
                    console.warn(`AudioManager: Failed to play sound '${soundName}':`, error);
                });
            }
            
        } catch (error) {
            console.warn(`AudioManager: Error playing sound '${soundName}':`, error);
        }
    }

    /**
     * Play click sound
     * Requirements: 12.1
     */
    playClickSound() {
        this.playSound('click', 0.8);
    }

    /**
     * Play purchase sound
     * Requirements: 12.1
     */
    playPurchaseSound() {
        this.playSound('purchase', 1.0);
    }

    /**
     * Play upgrade sound
     * Requirements: 12.1
     */
    playUpgradeSound() {
        this.playSound('upgrade', 1.0);
    }

    /**
     * Play event notification sound
     * Requirements: 12.3
     * @param {string} eventType - Type of event for different sound variations
     */
    playEventSound(eventType = 'default') {
        // Use different sounds based on event type
        switch (eventType) {
            case 'error':
            case 'crisis':
                this.playSound('error', 0.9);
                break;
            case 'milestone':
            case 'achievement':
                this.playSound('milestone', 1.0);
                break;
            default:
                this.playSound('event', 0.8);
        }
    }

    /**
     * Play victory sound
     * Requirements: 12.5
     */
    playVictorySound() {
        this.playSound('victory', 1.0);
    }

    /**
     * Play notification sound
     * Requirements: 12.3
     */
    playNotificationSound() {
        this.playSound('notification', 0.7);
    }

    /**
     * Set master volume
     * @param {number} level - Volume level (0.0 to 1.0)
     */
    setVolume(level) {
        this.volume = Math.max(0.0, Math.min(1.0, level));
        
        if (this.masterGainNode) {
            this.masterGainNode.gain.value = this.volume;
        }
        
        // Update all loaded sounds
        for (const sound of this.sounds.values()) {
            if (sound) {
                sound.volume = this.volume;
            }
        }
    }

    /**
     * Toggle mute state
     */
    toggleMute() {
        this.isMuted = !this.isMuted;
        
        if (this.masterGainNode) {
            this.masterGainNode.gain.value = this.isMuted ? 0 : this.volume;
        }
        
        return this.isMuted;
    }

    /**
     * Enable or disable audio system
     * @param {boolean} enabled - Whether audio should be enabled
     */
    setEnabled(enabled) {
        this.isEnabled = enabled;
    }

    /**
     * Get current audio settings
     */
    getSettings() {
        return {
            isEnabled: this.isEnabled,
            volume: this.volume,
            isMuted: this.isMuted,
            soundsLoaded: this.sounds.size
        };
    }

    /**
     * Resume audio context (required for some browsers)
     */
    resumeAudioContext() {
        if (this.audioContext && this.audioContext.state === 'suspended') {
            this.audioContext.resume();
        }
    }
}
