/**
 * Utils.js - Core Interfaces and Utility Functions
 * Provides common utilities, constants, and helper functions for the Pooristan game
 */

// Game Constants
const GAME_CONFIG = {
    VERSION: "1.0.0",
    SAVE_INTERVAL: 30000, // 30 seconds
    TICK_RATE: 1000 / 60, // 60 FPS
    AUTO_SAVE_KEY: 'pooristan_autosave',
    
    // Resource types
    RESOURCE_TYPES: {
        GDP: 'gdp',
        FOOD: 'food',
        INDUSTRY: 'industry',
        KNOWLEDGE: 'knowledge',
        INFLUENCE: 'influence',
        BITCOIN: 'bitcoin'
    },
    
    // Generator types
    GENERATOR_TYPES: {
        FARM: 'farm',
        FACTORY: 'factory',
        SCHOOL: 'school',
        EMBASSY: 'embassy',
        BITCOIN_MINER: 'bitcoinMiner'
    },
    
    // Event types
    EVENT_TYPES: {
        LOCAL_CRISIS: 'localCrisis',
        INTERNATIONAL_POLITICS: 'internationalPolitics',
        IMF_LOAN: 'imfLoan'
    },
    
    // UI Constants
    UI: {
        ANIMATION_DURATION: 300,
        NOTIFICATION_DURATION: 3000,
        MODAL_FADE_DURATION: 200
    }
};

// Core Interfaces and Classes

/**
 * Base Resource Interface
 */
class Resource {
    constructor(type, amount = 0, perSecond = 0) {
        this.type = type;
        this.amount = amount;
        this.perSecond = perSecond;
        this.totalEarned = amount;
    }
    
    add(amount) {
        this.amount += amount;
        this.totalEarned += amount;
    }
    
    subtract(amount) {
        if (this.amount >= amount) {
            this.amount -= amount;
            return true;
        }
        return false;
    }
    
    canAfford(cost) {
        return this.amount >= cost;
    }
}

/**
 * Base Generator Interface
 */
class Generator {
    constructor(type, config) {
        this.type = type;
        this.name = config.name;
        this.description = config.description;
        this.count = 0;
        this.baseProduction = config.baseProduction;
        this.baseCost = config.baseCost;
        this.costMultiplier = config.costMultiplier;
        this.resourceType = config.resourceType;
        this.maxUpgradeLevel = config.maxUpgradeLevel || 10;
        this.unlockRequirement = config.unlockRequirement;
        
        this.upgrades = {
            level: 0,
            productionMultiplier: 1,
            costReduction: 1
        };
    }
    
    getCurrentCost(quantity = 1) {
        let totalCost = 0;
        for (let i = 0; i < quantity; i++) {
            totalCost += this.baseCost * Math.pow(this.costMultiplier, this.count + i);
        }
        return Math.floor(totalCost * this.upgrades.costReduction);
    }
    
    getCurrentProduction() {
        return this.baseProduction * this.count * this.upgrades.productionMultiplier;
    }
    
    getUpgradeCost() {
        const baseCost = this.baseCost * 5; // Upgrade costs 5x base cost
        return Math.floor(baseCost * Math.pow(2, this.upgrades.level));
    }
    
    canUpgrade() {
        return this.upgrades.level < this.maxUpgradeLevel && this.count > 0;
    }
}

/**
 * Event Interface
 */
class GameEvent {
    constructor(id, type, config) {
        this.id = id;
        this.type = type;
        this.name = config.name;
        this.description = config.description;
        this.probability = config.probability || 0.1;
        this.duration = config.duration || 0;
        this.effects = config.effects || {};
        this.choices = config.choices || [];
        this.isActive = false;
        this.startTime = 0;
    }
    
    activate() {
        this.isActive = true;
        this.startTime = Date.now();
    }
    
    deactivate() {
        this.isActive = false;
        this.startTime = 0;
    }
    
    isExpired() {
        if (this.duration === 0) return false;
        return Date.now() - this.startTime > this.duration;
    }
}

// Utility Functions

/**
 * Format numbers for display
 */
const NumberFormatter = {
    /**
     * Format large numbers with appropriate suffixes
     */
    format(num) {
        if (num < 1000) return Math.floor(num).toString();
        if (num < 1000000) return (num / 1000).toFixed(1) + 'K';
        if (num < 1000000000) return (num / 1000000).toFixed(1) + 'M';
        if (num < 1000000000000) return (num / 1000000000).toFixed(1) + 'B';
        return (num / 1000000000000).toFixed(1) + 'T';
    },
    
    /**
     * Format currency values
     */
    formatCurrency(num) {
        return '$' + this.format(num);
    },
    
    /**
     * Format decimal numbers
     */
    formatDecimal(num, decimals = 2) {
        return num.toFixed(decimals);
    },
    
    /**
     * Format percentage
     */
    formatPercent(num) {
        return (num * 100).toFixed(1) + '%';
    }
};

/**
 * Time utilities
 */
const TimeUtils = {
    /**
     * Format milliseconds to readable time
     */
    formatTime(ms) {
        const seconds = Math.floor(ms / 1000);
        const minutes = Math.floor(seconds / 60);
        const hours = Math.floor(minutes / 60);
        
        if (hours > 0) {
            return `${hours}:${(minutes % 60).toString().padStart(2, '0')}:${(seconds % 60).toString().padStart(2, '0')}`;
        }
        return `${minutes}:${(seconds % 60).toString().padStart(2, '0')}`;
    },
    
    /**
     * Get current timestamp
     */
    now() {
        return Date.now();
    },
    
    /**
     * Calculate delta time between timestamps
     */
    delta(start, end = Date.now()) {
        return end - start;
    },

    /**
     * Format time ago (e.g., "2 minutes ago")
     */
    formatTimeAgo(ms) {
        const seconds = Math.floor(ms / 1000);
        const minutes = Math.floor(seconds / 60);
        const hours = Math.floor(minutes / 60);
        const days = Math.floor(hours / 24);

        if (days > 0) {
            return `${days} day${days > 1 ? 's' : ''} ago`;
        } else if (hours > 0) {
            return `${hours} hour${hours > 1 ? 's' : ''} ago`;
        } else if (minutes > 0) {
            return `${minutes} minute${minutes > 1 ? 's' : ''} ago`;
        } else {
            return `${seconds} second${seconds > 1 ? 's' : ''} ago`;
        }
    }
};

/**
 * DOM utilities
 */
const DOMUtils = {
    /**
     * Safely get element by ID
     */
    getElementById(id) {
        const element = document.getElementById(id);
        if (!element) {
            console.warn(`Element with ID '${id}' not found`);
        }
        return element;
    },
    
    /**
     * Safely query selector
     */
    querySelector(selector) {
        const element = document.querySelector(selector);
        if (!element) {
            console.warn(`Element with selector '${selector}' not found`);
        }
        return element;
    },
    
    /**
     * Add class with animation support
     */
    addClass(element, className, duration = 0) {
        if (!element) return;
        element.classList.add(className);
        
        if (duration > 0) {
            setTimeout(() => {
                element.classList.remove(className);
            }, duration);
        }
    },
    
    /**
     * Remove class
     */
    removeClass(element, className) {
        if (!element) return;
        element.classList.remove(className);
    },
    
    /**
     * Toggle class
     */
    toggleClass(element, className) {
        if (!element) return;
        element.classList.toggle(className);
    },
    
    /**
     * Set element text content safely
     */
    setText(element, text) {
        if (!element) return;
        element.textContent = text;
    },
    
    /**
     * Set element HTML safely
     */
    setHTML(element, html) {
        if (!element) return;
        element.innerHTML = html;
    },
    
    /**
     * Create element with attributes
     */
    createElement(tag, attributes = {}, textContent = '') {
        const element = document.createElement(tag);
        
        Object.keys(attributes).forEach(key => {
            if (key === 'className') {
                element.className = attributes[key];
            } else {
                element.setAttribute(key, attributes[key]);
            }
        });
        
        if (textContent) {
            element.textContent = textContent;
        }
        
        return element;
    }
};

/**
 * Local Storage utilities
 */
const StorageUtils = {
    /**
     * Save data to localStorage
     */
    save(key, data) {
        try {
            const jsonData = JSON.stringify(data);
            localStorage.setItem(key, jsonData);
            return true;
        } catch (error) {
            console.error('Failed to save to localStorage:', error);
            return false;
        }
    },
    
    /**
     * Load data from localStorage
     */
    load(key, defaultValue = null) {
        try {
            const jsonData = localStorage.getItem(key);
            if (jsonData === null) return defaultValue;
            return JSON.parse(jsonData);
        } catch (error) {
            console.error('Failed to load from localStorage:', error);
            return defaultValue;
        }
    },
    
    /**
     * Remove data from localStorage
     */
    remove(key) {
        try {
            localStorage.removeItem(key);
            return true;
        } catch (error) {
            console.error('Failed to remove from localStorage:', error);
            return false;
        }
    },
    
    /**
     * Clear all localStorage
     */
    clear() {
        try {
            localStorage.clear();
            return true;
        } catch (error) {
            console.error('Failed to clear localStorage:', error);
            return false;
        }
    }
};

/**
 * Math utilities
 */
const MathUtils = {
    /**
     * Clamp value between min and max
     */
    clamp(value, min, max) {
        return Math.min(Math.max(value, min), max);
    },
    
    /**
     * Linear interpolation
     */
    lerp(start, end, factor) {
        return start + (end - start) * factor;
    },
    
    /**
     * Random number between min and max
     */
    random(min, max) {
        return Math.random() * (max - min) + min;
    },
    
    /**
     * Random integer between min and max (inclusive)
     */
    randomInt(min, max) {
        return Math.floor(Math.random() * (max - min + 1)) + min;
    },
    
    /**
     * Check if random event should occur based on probability
     */
    shouldOccur(probability) {
        return Math.random() < probability;
    },
    
    /**
     * Calculate exponential cost scaling
     */
    exponentialCost(baseCost, count, multiplier) {
        return Math.floor(baseCost * Math.pow(multiplier, count));
    }
};

/**
 * Validation utilities
 */
const ValidationUtils = {
    /**
     * Validate game state structure
     */
    validateGameState(state) {
        if (!state || typeof state !== 'object') return false;
        
        // Check required properties
        const requiredProps = ['gdp', 'resources', 'generators', 'version'];
        for (const prop of requiredProps) {
            if (!(prop in state)) {
                console.warn(`Missing required property: ${prop}`);
                return false;
            }
        }
        
        return true;
    },
    
    /**
     * Validate resource amounts
     */
    validateResourceAmount(amount) {
        return typeof amount === 'number' && amount >= 0 && isFinite(amount);
    },
    
    /**
     * Validate generator count
     */
    validateGeneratorCount(count) {
        return typeof count === 'number' && count >= 0 && Number.isInteger(count);
    }
};

/**
 * Error handling utilities
 */
const ErrorHandler = {
    /**
     * Handle and log errors
     */
    handle(error, context = 'Unknown') {
        console.error(`Error in ${context}:`, error);
        
        // Could be extended to send error reports, show user notifications, etc.
        return {
            success: false,
            error: error.message || 'Unknown error occurred'
        };
    },
    
    /**
     * Safely execute function with error handling
     */
    safeExecute(fn, context = 'Function', defaultReturn = null) {
        try {
            return fn();
        } catch (error) {
            this.handle(error, context);
            return defaultReturn;
        }
    }
};

// Export utilities for global access
window.GAME_CONFIG = GAME_CONFIG;
window.Resource = Resource;
window.Generator = Generator;
window.GameEvent = GameEvent;
window.NumberFormatter = NumberFormatter;
window.TimeUtils = TimeUtils;
window.DOMUtils = DOMUtils;
window.StorageUtils = StorageUtils;
window.MathUtils = MathUtils;
window.ValidationUtils = ValidationUtils;
window.ErrorHandler = ErrorHandler;

// Export for potential module usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        GAME_CONFIG,
        Resource,
        Generator,
        GameEvent,
        NumberFormatter,
        TimeUtils,
        DOMUtils,
        StorageUtils,
        MathUtils,
        ValidationUtils,
        ErrorHandler
    };
}