/**
 * AnimationManager - Handles visual animations and effects for Pooristan
 * Manages UI animations, particle effects, and visual feedback
 * Requirements: 12.2, 12.4, 12.5
 */
class AnimationManager {
    constructor() {
        this.activeAnimations = new Map();
        this.animationId = 0;
        this.isEnabled = true;
        this.animationSpeed = 1.0; // Animation speed multiplier
        
        // Animation configurations
        this.animationConfigs = {
            resourceGain: {
                duration: 1000,
                easing: 'ease-out',
                scale: 1.2,
                opacity: [1, 0]
            },
            purchase: {
                duration: 300,
                easing: 'ease-in-out',
                scale: [1, 1.1, 1],
                glow: true
            },
            upgrade: {
                duration: 500,
                easing: 'ease-out',
                scale: [1, 1.2, 1],
                rotation: 360,
                glow: true
            },
            event: {
                duration: 800,
                easing: 'ease-out',
                shake: true,
                highlight: true
            },
            milestone: {
                duration: 1500,
                easing: 'ease-out',
                scale: [1, 1.3, 1],
                glow: true,
                particles: true
            }
        };
        
        // Initialize animation system
        this.initialize();
    }

    /**
     * Initialize animation system
     * Requirements: 12.2
     */
    initialize() {
        try {
            // Add CSS animations if not already present
            this.injectAnimationStyles();
            
            // Start animation update loop
            this.startAnimationLoop();
            
            console.log('AnimationManager: Initialized successfully');
        } catch (error) {
            console.warn('AnimationManager: Failed to initialize:', error);
            this.isEnabled = false;
        }
    }

    /**
     * Inject CSS animation styles
     */
    injectAnimationStyles() {
        if (document.querySelector('#animation-styles')) return;
        
        const style = document.createElement('style');
        style.id = 'animation-styles';
        style.textContent = `
            @keyframes resource-gain {
                0% { transform: translate(-50%, -50%) scale(1); opacity: 1; }
                50% { transform: translate(-50%, -75%) scale(1.2); opacity: 0.8; }
                100% { transform: translate(-50%, -100%) scale(0.8); opacity: 0; }
            }
            
            @keyframes purchase-glow {
                0% { box-shadow: 0 0 5px rgba(46, 204, 113, 0.5); }
                50% { box-shadow: 0 0 20px rgba(46, 204, 113, 0.8); }
                100% { box-shadow: 0 0 5px rgba(46, 204, 113, 0.5); }
            }
            
            @keyframes upgrade-spin {
                0% { transform: rotate(0deg) scale(1); }
                50% { transform: rotate(180deg) scale(1.2); }
                100% { transform: rotate(360deg) scale(1); }
            }
            
            @keyframes shake {
                0%, 100% { transform: translateX(0); }
                25% { transform: translateX(-5px); }
                75% { transform: translateX(5px); }
            }
            
            @keyframes pulse {
                0% { transform: scale(1); opacity: 1; }
                50% { transform: scale(1.1); opacity: 0.8; }
                100% { transform: scale(1); opacity: 1; }
            }
            
            @keyframes milestone-celebration {
                0% { transform: scale(1) rotate(0deg); opacity: 1; }
                25% { transform: scale(1.2) rotate(5deg); opacity: 0.9; }
                50% { transform: scale(1.3) rotate(-5deg); opacity: 0.8; }
                75% { transform: scale(1.1) rotate(3deg); opacity: 0.9; }
                100% { transform: scale(1) rotate(0deg); opacity: 1; }
            }
            
            .animation-glow {
                box-shadow: 0 0 15px rgba(46, 204, 113, 0.6);
                transition: box-shadow 0.3s ease;
            }
            
            .animation-highlight {
                background-color: rgba(255, 255, 0, 0.2);
                transition: background-color 0.3s ease;
            }
            
            .particle {
                position: absolute;
                width: 4px;
                height: 4px;
                background: radial-gradient(circle, #ffd700, #ff6b35);
                border-radius: 50%;
                pointer-events: none;
                z-index: 1000;
            }
        `;
        document.head.appendChild(style);
    }

    /**
     * Start animation update loop
     */
    startAnimationLoop() {
        const updateAnimations = () => {
            this.updateAnimations();
            requestAnimationFrame(updateAnimations);
        };
        requestAnimationFrame(updateAnimations);
    }

    /**
     * Update all active animations
     * @param {number} deltaTime - Time since last update
     */
    updateAnimations(deltaTime = 16) {
        if (!this.isEnabled) return;
        
        const currentTime = Date.now();
        const animationsToRemove = [];
        
        for (const [id, animation] of this.activeAnimations) {
            const elapsed = currentTime - animation.startTime;
            const progress = Math.min(elapsed / animation.duration, 1);
            
            if (progress >= 1) {
                // Animation complete
                this.completeAnimation(animation);
                animationsToRemove.push(id);
            } else {
                // Update animation
                this.updateAnimation(animation, progress);
            }
        }
        
        // Remove completed animations
        animationsToRemove.forEach(id => this.activeAnimations.delete(id));
    }

    /**
     * Animate resource gain
     * Requirements: 12.4
     * @param {string} resourceType - Type of resource gained
     * @param {number} amount - Amount gained
     * @param {HTMLElement} element - Target element
     */
    animateResourceGain(resourceType, amount, element) {
        if (!this.isEnabled || !element) return;
        
        const rect = element.getBoundingClientRect();
        const floatingText = document.createElement('div');
        
        // Format the amount display
        const displayAmount = amount >= 1000 ? 
            `+${(amount / 1000).toFixed(1)}k` : 
            `+${Math.floor(amount)}`;
        
        floatingText.textContent = displayAmount;
        floatingText.className = 'floating-resource-gain';
        
        // Style the floating text
        Object.assign(floatingText.style, {
            position: 'absolute',
            left: `${rect.left + rect.width / 2}px`,
            top: `${rect.top + rect.height / 2}px`,
            transform: 'translate(-50%, -50%)',
            color: this.getResourceColor(resourceType),
            fontWeight: 'bold',
            fontSize: '1.1rem',
            pointerEvents: 'none',
            zIndex: '1000',
            textShadow: '0 0 3px rgba(0,0,0,0.7)',
            animation: `resource-gain ${this.animationConfigs.resourceGain.duration * this.animationSpeed}ms ease-out forwards`
        });
        
        document.body.appendChild(floatingText);
        
        // Remove after animation
        setTimeout(() => {
            if (floatingText.parentNode) {
                floatingText.parentNode.removeChild(floatingText);
            }
        }, this.animationConfigs.resourceGain.duration * this.animationSpeed);
    }

    /**
     * Animate purchase action
     * Requirements: 12.2
     * @param {string} generatorType - Type of generator purchased
     */
    animatePurchase(generatorType) {
        if (!this.isEnabled) return;
        
        const button = document.querySelector(`.purchase-btn[data-type="${generatorType}"]`);
        if (!button) return;
        
        // Add glow effect
        button.classList.add('animation-glow');
        
        // Scale animation
        const originalTransform = button.style.transform;
        button.style.transition = `transform ${this.animationConfigs.purchase.duration}ms ${this.animationConfigs.purchase.easing}`;
        button.style.transform = 'scale(1.1)';
        
        setTimeout(() => {
            button.style.transform = originalTransform;
            button.classList.remove('animation-glow');
            button.style.transition = '';
        }, this.animationConfigs.purchase.duration * this.animationSpeed);
    }

    /**
     * Animate upgrade action
     * Requirements: 12.2
     * @param {string} generatorType - Type of generator upgraded
     */
    animateUpgrade(generatorType) {
        if (!this.isEnabled) return;
        
        const upgradeButton = document.querySelector(`.upgrade-btn[data-type="${generatorType}"]`);
        if (!upgradeButton) return;
        
        // Add glow and spin animation
        upgradeButton.classList.add('animation-glow');
        upgradeButton.style.animation = `upgrade-spin ${this.animationConfigs.upgrade.duration * this.animationSpeed}ms ease-out`;
        
        setTimeout(() => {
            upgradeButton.classList.remove('animation-glow');
            upgradeButton.style.animation = '';
        }, this.animationConfigs.upgrade.duration * this.animationSpeed);
    }

    /**
     * Animate event occurrence
     * Requirements: 12.2, 12.3
     * @param {string} eventType - Type of event
     */
    animateEvent(eventType) {
        if (!this.isEnabled) return;
        
        const eventTab = document.querySelector('.tab-btn[data-tab="events"]');
        if (eventTab) {
            // Add shake and highlight effect
            eventTab.style.animation = `shake 0.5s ease-in-out`;
            eventTab.classList.add('animation-highlight');
            
            setTimeout(() => {
                eventTab.style.animation = '';
                eventTab.classList.remove('animation-highlight');
            }, 500);
        }
        
        // Create screen flash for important events
        if (eventType === 'crisis' || eventType === 'imfLoan') {
            this.createScreenFlash();
        }
    }

    /**
     * Animate milestone achievement
     * Requirements: 12.5
     * @param {string} milestoneType - Type of milestone
     * @param {HTMLElement} targetElement - Element to animate
     */
    animateMilestone(milestoneType, targetElement = null) {
        if (!this.isEnabled) return;
        
        const target = targetElement || document.querySelector('.gdp-display');
        if (!target) return;
        
        // Celebration animation
        target.style.animation = `milestone-celebration ${this.animationConfigs.milestone.duration * this.animationSpeed}ms ease-out`;
        target.classList.add('animation-glow');
        
        // Create particle effect
        this.createParticleEffect(target);
        
        setTimeout(() => {
            target.style.animation = '';
            target.classList.remove('animation-glow');
        }, this.animationConfigs.milestone.duration * this.animationSpeed);
    }

    /**
     * Create particle effect
     * @param {HTMLElement} sourceElement - Element to emit particles from
     */
    createParticleEffect(sourceElement) {
        if (!sourceElement) return;
        
        const rect = sourceElement.getBoundingClientRect();
        const particleCount = 12;
        
        for (let i = 0; i < particleCount; i++) {
            const particle = document.createElement('div');
            particle.className = 'particle';
            
            const angle = (i / particleCount) * Math.PI * 2;
            const velocity = 50 + Math.random() * 30;
            const startX = rect.left + rect.width / 2;
            const startY = rect.top + rect.height / 2;
            
            particle.style.left = `${startX}px`;
            particle.style.top = `${startY}px`;
            
            document.body.appendChild(particle);
            
            // Animate particle
            const endX = startX + Math.cos(angle) * velocity;
            const endY = startY + Math.sin(angle) * velocity;
            
            particle.animate([
                { transform: 'translate(0, 0) scale(1)', opacity: 1 },
                { transform: `translate(${endX - startX}px, ${endY - startY}px) scale(0)`, opacity: 0 }
            ], {
                duration: 1000,
                easing: 'ease-out'
            }).onfinish = () => {
                if (particle.parentNode) {
                    particle.parentNode.removeChild(particle);
                }
            };
        }
    }

    /**
     * Create screen flash effect
     */
    createScreenFlash() {
        const flash = document.createElement('div');
        flash.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            background: rgba(255, 255, 255, 0.3);
            pointer-events: none;
            z-index: 9999;
        `;
        
        document.body.appendChild(flash);
        
        flash.animate([
            { opacity: 0 },
            { opacity: 1 },
            { opacity: 0 }
        ], {
            duration: 200,
            easing: 'ease-out'
        }).onfinish = () => {
            if (flash.parentNode) {
                flash.parentNode.removeChild(flash);
            }
        };
    }

    /**
     * Get color for resource type
     * @param {string} resourceType - Type of resource
     * @returns {string} CSS color value
     */
    getResourceColor(resourceType) {
        const colors = {
            gdp: '#2ecc71',
            food: '#e67e22',
            industry: '#3498db',
            knowledge: '#9b59b6',
            influence: '#e74c3c',
            bitcoin: '#f39c12'
        };
        return colors[resourceType] || '#2ecc71';
    }

    /**
     * Start animation with custom configuration
     * @param {string} animationId - Unique animation identifier
     * @param {Object} config - Animation configuration
     */
    startAnimation(animationId, config) {
        if (!this.isEnabled) return;
        
        const animation = {
            id: animationId,
            startTime: Date.now(),
            duration: config.duration || 1000,
            ...config
        };
        
        this.activeAnimations.set(animationId, animation);
    }

    /**
     * Stop animation
     * @param {string} animationId - Animation identifier to stop
     */
    stopAnimation(animationId) {
        const animation = this.activeAnimations.get(animationId);
        if (animation) {
            this.completeAnimation(animation);
            this.activeAnimations.delete(animationId);
        }
    }

    /**
     * Update individual animation
     * @param {Object} animation - Animation object
     * @param {number} progress - Animation progress (0-1)
     */
    updateAnimation(animation, progress) {
        // Custom animation update logic can be added here
        if (animation.onUpdate) {
            animation.onUpdate(progress);
        }
    }

    /**
     * Complete animation
     * @param {Object} animation - Animation object
     */
    completeAnimation(animation) {
        if (animation.onComplete) {
            animation.onComplete();
        }
    }

    /**
     * Set animation speed multiplier
     * @param {number} speed - Speed multiplier (1.0 = normal)
     */
    setAnimationSpeed(speed) {
        this.animationSpeed = Math.max(0.1, Math.min(3.0, speed));
    }

    /**
     * Enable or disable animations
     * @param {boolean} enabled - Whether animations should be enabled
     */
    setEnabled(enabled) {
        this.isEnabled = enabled;
        
        if (!enabled) {
            // Clear all active animations
            this.activeAnimations.clear();
        }
    }

    /**
     * Get animation settings
     */
    getSettings() {
        return {
            isEnabled: this.isEnabled,
            animationSpeed: this.animationSpeed,
            activeAnimations: this.activeAnimations.size
        };
    }
}
