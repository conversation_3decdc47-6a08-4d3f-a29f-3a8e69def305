/**
 * GameEngine - Core game logic and state management for Pooristan
 * Handles GDP clicking, resource generation, and game loop management
 */
class GameEngine {
    constructor(orangeSDK = null) {
        this.gameState = this.getDefaultGameState();
        this.gameLoop = null;
        this.lastUpdateTime = 0;
        this.fixedTimeStep = 1000 / 60; // 60 FPS
        this.accumulator = 0;
        this.isRunning = false;
        this.isPaused = false;
        this.pauseStartTime = 0;
        this.totalPausedTime = 0;
        this.lastSaveTime = 0;
        this.saveInterval = 30000; // Save every 30 seconds
        this.orangeSDK = orangeSDK;
        this.uiManager = null;
        this.technologyManager = null;
        this.eventManager = null;

        // Bind methods to preserve context
        this.tick = this.tick.bind(this);
        this.loop = this.loop.bind(this);
    }

    /**
     * Set UI Manager reference
     */
    setUIManager(uiManager) {
        this.uiManager = uiManager;
    }

    /**
     * Set Technology Manager reference
     */
    setTechnologyManager(technologyManager) {
        this.technologyManager = technologyManager;
    }

    /**
     * Set Event Manager reference
     * Requirements: 9.1
     */
    setEventManager(eventManager) {
        this.eventManager = eventManager;
    }

    /**
     * Set Technology Manager reference
     * Requirements: 8.1, 8.2, 8.3, 8.4, 8.5
     */
    setTechnologyManager(technologyManager) {
        this.technologyManager = technologyManager;
    }

    /**
     * Get default game state structure
     */
    getDefaultGameState() {
        return {
            // Core resources
            gdp: 0,
            gdpPerSecond: 0,
            totalGDPEarned: 0,
            
            // Secondary resources
            resources: {
                food: 0,
                industry: 0,
                knowledge: 0,
                influence: 0,
                bitcoin: 0
            },
            
            // Resource generation rates
            resourcesPerSecond: {
                food: 0,
                industry: 0,
                knowledge: 0,
                influence: 0,
                bitcoin: 0
            },
            
            // Generators for all resource types
            generators: {
                farm: {
                    count: 0,
                    baseProduction: 1,
                    baseCost: { gdp: 10 },
                    costMultiplier: 1.15,
                    resourceType: 'food',
                    unlockRequirement: null,
                    unlocked: true,
                    upgrades: {
                        level: 0,
                        productionMultiplier: 1
                    }
                },
                factory: {
                    count: 0,
                    baseProduction: 2,
                    baseCost: { gdp: 100 },
                    costMultiplier: 1.2,
                    resourceType: 'industry',
                    unlockRequirement: 'industrialization',
                    unlocked: false,
                    upgrades: {
                        level: 0,
                        productionMultiplier: 1
                    }
                },
                school: {
                    count: 0,
                    baseProduction: 0.5,
                    baseCost: { food: 100, industry: 50 },
                    costMultiplier: 1.25,
                    resourceType: 'knowledge',
                    unlockRequirement: 'education',
                    unlocked: false,
                    upgrades: {
                        level: 0,
                        productionMultiplier: 1
                    }
                },
                embassy: {
                    count: 0,
                    baseProduction: 0.1,
                    baseCost: { knowledge: 100, industry: 100 },
                    costMultiplier: 1.3,
                    resourceType: 'influence',
                    unlockRequirement: 'diplomacy',
                    unlocked: false,
                    upgrades: {
                        level: 0,
                        productionMultiplier: 1
                    }
                },
                bitcoinMiner: {
                    count: 0,
                    baseProduction: 0.01,
                    baseCost: { knowledge: 500, industry: 1000 },
                    costMultiplier: 1.4,
                    resourceType: 'bitcoin',
                    unlockRequirement: 'cryptocurrency',
                    unlocked: false,
                    upgrades: {
                        level: 0,
                        productionMultiplier: 1
                    }
                }
            },

            // Technology tree state
            // Requirements: 8.1, 8.2, 8.3
            technologies: {
                researched: [],
                available: ['basicAgriculture', 'industrialization', 'education'],
                inProgress: null,
                researchProgress: 0
            },

            // Technology multipliers (applied by TechnologyManager)
            // Requirements: 8.4, 8.5
            globalMultipliers: {},
            influenceMultipliers: {},
            researchTimeReductions: {},

            // Event system state
            // Requirements: 9.1, 9.2, 9.3, 9.4, 9.5
            events: {
                activeEffects: [],
                eventHistory: []
            },
            eventMultipliers: {
                resources: {},
                generators: {}
            },

            // Victory conditions
            victory: {
                influenceTarget: 100,
                currentInfluence: 0,
                gameWon: false,
                finalScore: 0,
                gameOverSent: false
            },

            // Game metadata
            gameStartTime: Date.now(),
            totalPlayTime: 0,
            totalPausedTime: 0,
            totalClicks: 0,

            // Version for save compatibility
            version: "1.0.0"
        };
    }

    /**
     * Handle GDP click - core clicking mechanism
     * Requirements: 1.1, 1.2
     */
    clickGDP() {
        this.gameState.gdp += 1;
        this.gameState.totalGDPEarned += 1;
        this.gameState.totalClicks += 1;
        
        // Validate state after click
        this.validateGameState();
        
        return this.gameState.gdp;
    }

    /**
     * Purchase generator with validation
     * Requirements: 2.1, 2.5, 7.4
     */
    purchaseGenerator(type, quantity = 1) {
        if (!this.gameState.generators[type]) {
            console.error(`Generator type ${type} not found`);
            return false;
        }

        const generator = this.gameState.generators[type];

        // Check if generator is unlocked
        if (!generator.unlocked) {
            console.warn(`Generator ${type} is not unlocked yet`);
            return false;
        }

        const cost = this.calculateGeneratorCost(type, quantity);

        // Check if player can afford the purchase
        if (!this.canAffordResourceCost(cost)) {
            return false;
        }

        // Deduct cost and add generators
        if (!this.deductResourceCost(cost)) {
            return false;
        }

        generator.count += quantity;

        // Recalculate resource rates
        this.updateResourceRates();

        // Validate state after purchase
        this.validateGameState();

        return true;
    }

    /**
     * Upgrade generator with validation
     * Requirements: 2.3, 2.4, 7.4
     */
    upgradeGenerator(type) {
        if (!this.gameState.generators[type]) {
            console.error(`Generator type ${type} not found`);
            return false;
        }

        const generator = this.gameState.generators[type];

        // Check if generator is unlocked and owned
        if (!generator.unlocked || generator.count === 0) {
            return false;
        }

        const upgradeCost = this.calculateUpgradeCost(type);

        // Check if player can afford the upgrade
        if (!this.canAffordResourceCost(upgradeCost)) {
            return false;
        }

        // Deduct cost and upgrade
        if (!this.deductResourceCost(upgradeCost)) {
            return false;
        }

        generator.upgrades.level += 1;
        generator.upgrades.productionMultiplier = 1 + (generator.upgrades.level * 0.5);

        // Recalculate resource rates
        this.updateResourceRates();

        // Validate state after upgrade
        this.validateGameState();

        return true;
    }

    /**
     * Calculate cost for purchasing generators
     * Uses exponential scaling based on current count
     * Requirements: 7.4, 8.4, 8.5
     */
    calculateGeneratorCost(type, quantity = 1) {
        const generator = this.gameState.generators[type];
        const baseCost = generator.baseCost;
        const currentCount = generator.count;
        const multiplier = generator.costMultiplier;

        // Calculate total cost for each resource type
        const totalCost = {};

        for (const resourceType in baseCost) {
            const resourceBaseCost = baseCost[resourceType];
            let resourceTotalCost = 0;

            for (let i = 0; i < quantity; i++) {
                resourceTotalCost += resourceBaseCost * Math.pow(multiplier, currentCount + i);
            }

            totalCost[resourceType] = Math.floor(resourceTotalCost);
        }

        // Apply technology cost reductions
        let costReduction = 1.0;
        if (generator.costReductions) {
            for (const reduction of Object.values(generator.costReductions)) {
                costReduction *= reduction;
            }
        }

        // Apply cost reduction to all resource types
        for (const resourceType in totalCost) {
            totalCost[resourceType] = Math.floor(totalCost[resourceType] * costReduction);
        }

        return totalCost;
    }

    /**
     * Calculate cost for upgrading a generator
     * Requirements: 7.4
     */
    calculateUpgradeCost(type) {
        const generator = this.gameState.generators[type];
        const baseCost = generator.baseCost;
        const currentLevel = generator.upgrades.level;

        // Calculate upgrade cost for each resource type (5x base cost, exponential scaling)
        const upgradeCost = {};

        for (const resourceType in baseCost) {
            const resourceBaseCost = baseCost[resourceType];
            const baseUpgradeCost = resourceBaseCost * 5; // Upgrade costs 5x base cost
            upgradeCost[resourceType] = Math.floor(baseUpgradeCost * Math.pow(2, currentLevel));
        }

        return upgradeCost;
    }

    /**
     * Calculate production for a specific generator
     * Requirements: 8.4, 8.5, 9.5
     */
    calculateGeneratorProduction(type) {
        const generator = this.gameState.generators[type];
        const baseProduction = generator.baseProduction;
        const count = generator.count;
        const upgradeMultiplier = generator.upgrades.productionMultiplier;

        // Apply technology multipliers
        let technologyMultiplier = 1.0;
        if (generator.technologyMultipliers) {
            for (const multiplier of Object.values(generator.technologyMultipliers)) {
                technologyMultiplier *= multiplier;
            }
        }

        // Apply global multipliers
        let globalMultiplier = 1.0;
        if (this.gameState.globalMultipliers) {
            for (const multiplier of Object.values(this.gameState.globalMultipliers)) {
                globalMultiplier *= multiplier;
            }
        }

        // Apply event multipliers
        // Requirements: 9.5
        let eventMultiplier = 1.0;
        if (this.gameState.eventMultipliers && this.gameState.eventMultipliers.generators) {
            eventMultiplier = this.gameState.eventMultipliers.generators[type] || 1.0;
        }

        return baseProduction * count * upgradeMultiplier * technologyMultiplier * globalMultiplier * eventMultiplier;
    }

    /**
     * Update resource generation rates
     * Requirements: 7.1, 7.2, 7.3, 8.4, 8.5
     */
    updateResourceRates() {
        // Reset all resource rates
        this.gameState.gdpPerSecond = 0;

        for (const resourceType in this.gameState.resourcesPerSecond) {
            this.gameState.resourcesPerSecond[resourceType] = 0;
        }

        // Calculate production from all generators
        for (const generatorType in this.gameState.generators) {
            const generator = this.gameState.generators[generatorType];
            const production = this.calculateGeneratorProduction(generatorType);
            const resourceType = generator.resourceType;

            // Add to specific resource rate
            if (resourceType && this.gameState.resourcesPerSecond.hasOwnProperty(resourceType)) {
                this.gameState.resourcesPerSecond[resourceType] += production;
            }

            // All generators also contribute to GDP
            this.gameState.gdpPerSecond += production;
        }
    }

    /**
     * Recalculate production rates (called by TechnologyManager when effects are applied)
     * Requirements: 8.4, 8.5
     */
    recalculateProductionRates() {
        this.updateResourceRates();
    }

    /**
     * Check victory conditions
     * Requirements: 8.4, 8.5
     */
    checkVictoryConditions() {
        const gameState = this.gameState;

        // Check if AI Development technology was researched (ultimate victory)
        if (gameState.technologies.researched.includes('aiDevelopment')) {
            if (!gameState.victory.gameWon) {
                gameState.victory.gameWon = true;
                gameState.victory.finalScore = this.calculateFinalScore();
                gameState.victory.gameOverSent = false; // Track if gameOver was sent to SDK

                console.log('VICTORY! AI Machine God Project completed!');

                // Show victory screen
                if (this.uiManager) {
                    // Show notification first
                    this.uiManager.showNotification(
                        'ULTIMATE VICTORY! You have achieved technological dominance through the AI Machine God Project!',
                        'victory'
                    );

                    // Show victory screen after a short delay
                    setTimeout(() => {
                        this.uiManager.showVictoryScreen({
                            type: 'aiDevelopment',
                            score: gameState.victory.finalScore
                        });
                    }, 2000);
                }

                return true;
            }
        }

        // Check influence-based victory (100% influence)
        const currentInfluence = gameState.resources.influence || 0;
        let influenceMultiplier = 1.0;

        // Apply influence multipliers from technologies
        if (gameState.influenceMultipliers) {
            for (const multiplier of Object.values(gameState.influenceMultipliers)) {
                influenceMultiplier *= multiplier;
            }
        }

        const effectiveInfluence = currentInfluence * influenceMultiplier;
        gameState.victory.currentInfluence = effectiveInfluence;

        if (effectiveInfluence >= gameState.victory.influenceTarget && !gameState.victory.gameWon) {
            gameState.victory.gameWon = true;
            gameState.victory.finalScore = this.calculateFinalScore();
            gameState.victory.gameOverSent = false; // Track if gameOver was sent to SDK

            console.log('VICTORY! 100% Global Influence achieved!');

            // Show victory screen
            if (this.uiManager) {
                // Show notification first
                this.uiManager.showNotification(
                    'VICTORY! You have achieved 100% Global Influence!',
                    'victory'
                );

                // Show victory screen after a short delay
                setTimeout(() => {
                    this.uiManager.showVictoryScreen({
                        type: 'influence',
                        score: gameState.victory.finalScore
                    });
                }, 2000);
            }

            return true;
        }

        return false;
    }

    /**
     * Calculate final score for victory
     * Requirements: 14.3, 14.5 - Consider total GDP, time played, and efficiency metrics
     */
    calculateFinalScore() {
        const gameState = this.gameState;
        const playTimeMinutes = gameState.totalPlayTime / (1000 * 60);
        const playTimeHours = playTimeMinutes / 60;

        // Base score from GDP earned
        let score = Math.floor(gameState.totalGDPEarned || gameState.gdp);

        // Efficiency bonus - reward faster completion
        // Maximum bonus of 10000 points for completing in under 30 minutes
        // Decreases linearly, minimum bonus of 100 points
        const maxBonusTime = 30; // minutes
        const maxBonus = 10000;
        const minBonus = 100;
        let efficiencyBonus = minBonus;

        if (playTimeMinutes < maxBonusTime) {
            const timeRatio = (maxBonusTime - playTimeMinutes) / maxBonusTime;
            efficiencyBonus = minBonus + (maxBonus - minBonus) * timeRatio;
        }
        score += Math.floor(efficiencyBonus);

        // Technology bonus - 1000 points per technology researched
        const techBonus = gameState.technologies.researched.length * 1000;
        score += techBonus;

        // Click efficiency bonus - reward players who used automation
        const clickEfficiency = gameState.totalClicks > 0 ?
            Math.min(gameState.totalGDPEarned / gameState.totalClicks, 1000) : 0;
        score += Math.floor(clickEfficiency * 10);

        // Resource diversity bonus - reward balanced resource management
        const resources = gameState.resources;
        const resourceTypes = ['food', 'industry', 'knowledge', 'influence', 'bitcoin'];
        const nonZeroResources = resourceTypes.filter(type => (resources[type] || 0) > 0).length;
        const diversityBonus = nonZeroResources * 500;
        score += diversityBonus;

        // Generator diversity bonus - reward using different generator types
        const generators = gameState.generators;
        const generatorTypes = ['farm', 'factory', 'school', 'embassy', 'bitcoinMiner'];
        const activeGenerators = generatorTypes.filter(type =>
            generators[type] && generators[type].count > 0
        ).length;
        const generatorBonus = activeGenerators * 1000;
        score += generatorBonus;

        // Victory type bonuses
        if (gameState.technologies.researched.includes('aiDevelopment')) {
            // Ultimate victory bonus - AI Machine God
            score += 50000;

            // Additional bonus for achieving both victory conditions
            if (gameState.victory.currentInfluence >= gameState.victory.influenceTarget) {
                score += 25000; // Double victory bonus
            }
        } else if (gameState.victory.currentInfluence >= gameState.victory.influenceTarget) {
            // Influence victory bonus
            score += 25000;
        }

        // Time penalty for very long games (over 2 hours)
        if (playTimeHours > 2) {
            const timePenalty = Math.floor((playTimeHours - 2) * 1000);
            score = Math.max(score - timePenalty, score * 0.5); // Never reduce by more than 50%
        }

        return Math.max(Math.floor(score), 0);
    }

    /**
     * Start the game loop
     * Requirements: 5.3, 5.4
     */
    startGameLoop() {
        if (this.isRunning) {
            return;
        }
        
        this.isRunning = true;
        this.lastUpdateTime = performance.now();
        this.gameLoop = requestAnimationFrame(this.loop);
    }

    /**
     * Stop the game loop
     */
    stopGameLoop() {
        if (this.gameLoop) {
            cancelAnimationFrame(this.gameLoop);
            this.gameLoop = null;
        }
        this.isRunning = false;
    }

    /**
     * Reset game to initial state
     * Requirements: 14.4, 14.5
     */
    resetGame() {
        // Stop current game loop
        this.stopGameLoop();

        // Reset to initial game state
        this.gameState = this.getDefaultGameState();

        // Reset timing
        this.lastUpdateTime = performance.now();
        this.lastSaveTime = performance.now();
        this.totalPausedTime = 0;
        this.pauseStartTime = null;
        this.isPaused = false;

        // Notify Orange SDK of reset
        if (this.orangeSDK) {
            this.orangeSDK.saveGameState(this.gameState);
        }

        console.log('Game reset to initial state');
    }

    /**
     * Pause the game - stops all resource generation and timers
     * Requirements: 13.1, 13.2, 13.3, 13.5
     */
    pauseGame() {
        if (!this.isRunning || this.isPaused) {
            return;
        }

        console.log('GameEngine: Pausing game');
        this.isPaused = true;
        this.pauseStartTime = performance.now();

        // Stop the game loop but keep isRunning true for resume
        if (this.gameLoop) {
            cancelAnimationFrame(this.gameLoop);
            this.gameLoop = null;
        }

        // Pause UI updates
        if (this.uiManager && typeof this.uiManager.pauseUI === 'function') {
            this.uiManager.pauseUI();
        }

        // Save game state when pausing
        this.saveGameState();

        console.log('GameEngine: Game paused successfully');
    }

    /**
     * Resume the game - continues from exact state when paused
     * Requirements: 13.1, 13.2, 13.3, 13.5
     */
    resumeGame() {
        if (!this.isRunning || !this.isPaused) {
            return;
        }

        console.log('GameEngine: Resuming game');

        // Calculate total paused time for accurate timing
        const pauseDuration = performance.now() - this.pauseStartTime;
        this.totalPausedTime += pauseDuration;

        // Reset timing variables to prevent time accumulation during pause
        this.lastUpdateTime = performance.now();
        this.accumulator = 0;

        // Clear pause state
        this.isPaused = false;
        this.pauseStartTime = 0;

        // Resume UI updates
        if (this.uiManager && typeof this.uiManager.resumeUI === 'function') {
            this.uiManager.resumeUI();
        }

        // Restart the game loop
        this.gameLoop = requestAnimationFrame(this.loop);

        console.log(`GameEngine: Game resumed successfully (paused for ${pauseDuration.toFixed(2)}ms)`);
    }

    /**
     * Quit the game with automatic save and cleanup
     * Requirements: 13.4
     */
    quitGame() {
        console.log('GameEngine: Quitting game');

        // Save current progress
        this.saveGameState();

        // Stop the game loop
        this.stopGameLoop();

        // Clean up UI
        if (this.uiManager && typeof this.uiManager.cleanup === 'function') {
            this.uiManager.cleanup();
        }

        // Clear any pending timers or intervals
        this.isPaused = false;
        this.pauseStartTime = 0;
        this.totalPausedTime = 0;
        this.lastUpdateTime = 0;
        this.accumulator = 0;
        this.lastSaveTime = 0;

        // Notify Orange SDK if available
        if (this.orangeSDK) {
            this.orangeSDK.gameQuit();
        }

        console.log('GameEngine: Game quit successfully');
    }

    /**
     * Main game loop with fixed timestep
     * Requirements: 5.3, 5.4, 13.1, 13.2, 13.5
     */
    loop(currentTime) {
        if (!this.isRunning || this.isPaused) {
            return;
        }

        const deltaTime = currentTime - this.lastUpdateTime;
        this.lastUpdateTime = currentTime;
        this.accumulator += deltaTime;

        // Fixed timestep updates
        while (this.accumulator >= this.fixedTimeStep) {
            this.tick(this.fixedTimeStep);
            this.accumulator -= this.fixedTimeStep;
        }

        // Continue the loop
        this.gameLoop = requestAnimationFrame(this.loop);
    }

    /**
     * Game tick - processes one frame of game logic
     * Requirements: 2.2, 5.3, 5.4, 7.3, 8.1, 9.1, 13.5
     */
    tick(deltaTime) {
        // Update total play time (excluding paused time)
        this.gameState.totalPlayTime += deltaTime;

        // Update total paused time in game state
        this.gameState.totalPausedTime = this.totalPausedTime;

        // Update technology research progress
        if (this.technologyManager) {
            this.technologyManager.updateResearchProgress(deltaTime);
        }

        // Check for events and update event system
        // Requirements: 9.1, 9.2, 9.3, 9.4
        if (this.eventManager) {
            this.eventManager.checkForEvents(deltaTime);
        }

        // Apply event effects to resource generation
        this.applyEventEffects();

        // Generate GDP from generators
        if (this.gameState.gdpPerSecond > 0) {
            const gdpGained = (this.gameState.gdpPerSecond * deltaTime) / 1000;
            this.gameState.gdp += gdpGained;
            this.gameState.totalGDPEarned += gdpGained;
        }

        // Generate secondary resources from generators
        for (const resourceType in this.gameState.resourcesPerSecond) {
            if (this.gameState.resourcesPerSecond[resourceType] > 0) {
                const resourceGained = (this.gameState.resourcesPerSecond[resourceType] * deltaTime) / 1000;
                this.gameState.resources[resourceType] += resourceGained;
            }
        }

        // Check for generator unlocks
        this.checkGeneratorUnlocks();

        // Check victory conditions
        this.checkVictoryConditions();

        // Check if it's time to save the game state
        const currentTime = performance.now();
        if (currentTime - this.lastSaveTime > this.saveInterval) {
            this.saveGameState();
            this.lastSaveTime = currentTime;
        }

        // Validate state after tick
        this.validateGameState();

        // Update UI if UI Manager is available
        if (this.uiManager) {
            this.uiManager.updateDisplays();
        }
    }

    /**
     * Apply event effects to resource generation
     * Requirements: 9.5
     */
    applyEventEffects() {
        if (!this.gameState.events || !this.gameState.events.activeEffects) {
            return;
        }

        const currentTime = Date.now();

        // Remove expired effects
        this.gameState.events.activeEffects = this.gameState.events.activeEffects.filter(effect => {
            if (effect.duration > 0 && currentTime - effect.startTime > effect.duration) {
                console.log('GameEngine: Event effect expired:', effect.id);
                return false;
            }
            return true;
        });

        // Apply active effects to resource generation rates
        let resourceMultipliers = {};
        let generatorMultipliers = {};

        this.gameState.events.activeEffects.forEach(effect => {
            if (effect.effects.resourceMultiplier) {
                for (const [resource, multiplier] of Object.entries(effect.effects.resourceMultiplier)) {
                    if (!resourceMultipliers[resource]) resourceMultipliers[resource] = 1;
                    resourceMultipliers[resource] *= multiplier;
                }
            }

            if (effect.effects.generatorMultiplier) {
                for (const [generator, multiplier] of Object.entries(effect.effects.generatorMultiplier)) {
                    if (!generatorMultipliers[generator]) generatorMultipliers[generator] = 1;
                    generatorMultipliers[generator] *= multiplier;
                }
            }
        });

        // Store multipliers for use in production calculations
        this.gameState.eventMultipliers = {
            resources: resourceMultipliers,
            generators: generatorMultipliers
        };
    }

    /**
     * Validate and correct game state
     * Requirements: 5.1, 5.2
     */
    validateGameState() {
        // Ensure GDP is not negative or NaN
        if (isNaN(this.gameState.gdp) || this.gameState.gdp < 0) {
            console.warn('Invalid GDP detected, correcting to 0');
            this.gameState.gdp = 0;
        }

        // Ensure totalGDPEarned is not negative or NaN
        if (isNaN(this.gameState.totalGDPEarned) || this.gameState.totalGDPEarned < 0) {
            console.warn('Invalid totalGDPEarned detected, correcting to 0');
            this.gameState.totalGDPEarned = 0;
        }
        
        // Ensure generator counts are valid
        for (const generatorType in this.gameState.generators) {
            const generator = this.gameState.generators[generatorType];
            
            if (isNaN(generator.count) || generator.count < 0) {
                console.warn(`Invalid generator count for ${generatorType}, correcting to 0`);
                generator.count = 0;
            }
            
            if (isNaN(generator.upgrades.level) || generator.upgrades.level < 0) {
                console.warn(`Invalid upgrade level for ${generatorType}, correcting to 0`);
                generator.upgrades.level = 0;
                generator.upgrades.productionMultiplier = 1;
            }
        }
        
        // Ensure totalClicks is valid
        if (isNaN(this.gameState.totalClicks) || this.gameState.totalClicks < 0) {
            console.warn('Invalid totalClicks detected, correcting to 0');
            this.gameState.totalClicks = 0;
        }

        // Ensure all resources are valid and not negative
        for (const resourceType in this.gameState.resources) {
            if (isNaN(this.gameState.resources[resourceType]) || this.gameState.resources[resourceType] < 0) {
                console.warn(`Invalid resource ${resourceType} detected, correcting to 0`);
                this.gameState.resources[resourceType] = 0;
            }
        }

        // Ensure resource rates are valid and not negative
        for (const resourceType in this.gameState.resourcesPerSecond) {
            if (isNaN(this.gameState.resourcesPerSecond[resourceType]) || this.gameState.resourcesPerSecond[resourceType] < 0) {
                console.warn(`Invalid resource rate ${resourceType} detected, correcting to 0`);
                this.gameState.resourcesPerSecond[resourceType] = 0;
            }
        }

        // Recalculate resource rates to ensure consistency
        this.updateResourceRates();
    }

    /**
     * Get current game state (for saving)
     */
    getGameState() {
        return JSON.parse(JSON.stringify(this.gameState));
    }

    /**
     * Load game state (from save data)
     */
    loadGameState(state) {
        if (!state || typeof state !== 'object') {
            console.warn('Invalid save state, using default');
            this.gameState = this.getDefaultGameState();
            return;
        }

        // Merge with default state to handle missing properties
        this.gameState = { ...this.getDefaultGameState(), ...state };

        // Restore pause timing state from saved data
        if (this.gameState.totalPausedTime) {
            this.totalPausedTime = this.gameState.totalPausedTime;
        }

        // Validate loaded state
        this.validateGameState();

        // Update calculated values
        this.updateResourceRates();
    }

    /**
     * Reset game to initial state
     */
    resetGame() {
        this.stopGameLoop();
        this.gameState = this.getDefaultGameState();
        this.lastUpdateTime = 0;
        this.accumulator = 0;
        this.isPaused = false;
        this.pauseStartTime = 0;
        this.totalPausedTime = 0;
        this.lastSaveTime = 0;
    }

    /**
     * Check if player can afford a purchase with GDP
     */
    canAfford(cost) {
        return this.gameState.gdp >= cost;
    }
    
    /**
     * Check if player can afford a resource cost
     * Requirements: 7.4
     */
    canAffordResourceCost(costs) {
        if (!costs) return true;
        
        // Check GDP cost
        if (costs.gdp && this.gameState.gdp < costs.gdp) {
            return false;
        }
        
        // Check resource costs
        for (const resourceType in this.gameState.resources) {
            if (costs[resourceType] && this.gameState.resources[resourceType] < costs[resourceType]) {
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * Deduct resources based on cost
     * Requirements: 7.4
     */
    deductResourceCost(costs) {
        if (!costs) return true;
        
        // Validate we can afford the cost first
        if (!this.canAffordResourceCost(costs)) {
            return false;
        }
        
        // Deduct GDP cost
        if (costs.gdp) {
            this.gameState.gdp -= costs.gdp;
        }
        
        // Deduct resource costs
        for (const resourceType in this.gameState.resources) {
            if (costs[resourceType]) {
                this.gameState.resources[resourceType] -= costs[resourceType];
            }
        }
        
        return true;
    }

    /**
     * Get generator information for UI
     * Requirements: 7.2, 7.3
     */
    getGeneratorInfo(type) {
        if (!this.gameState.generators[type]) {
            return null;
        }

        const generator = this.gameState.generators[type];
        const cost = this.calculateGeneratorCost(type);
        const upgradeCost = this.calculateUpgradeCost(type);

        return {
            count: generator.count,
            cost: cost,
            production: this.calculateGeneratorProduction(type),
            upgradeLevel: generator.upgrades.level,
            upgradeCost: upgradeCost,
            canAffordPurchase: this.canAffordResourceCost(cost),
            canAffordUpgrade: generator.count > 0 && this.canAffordResourceCost(upgradeCost),
            resourceType: generator.resourceType,
            unlocked: generator.unlocked,
            unlockRequirement: generator.unlockRequirement
        };
    }
    
    /**
     * Get resource information for UI
     * Requirements: 7.1, 7.2
     */
    getResourceInfo(resourceType) {
        if (!this.gameState.resources.hasOwnProperty(resourceType)) {
            return null;
        }
        
        return {
            amount: this.gameState.resources[resourceType],
            perSecond: this.gameState.resourcesPerSecond[resourceType]
        };
    }
    
    /**
     * Get all resources information
     * Requirements: 7.1, 7.2
     */
    getAllResourcesInfo() {
        const resources = {};

        // Add GDP as a special resource
        resources.gdp = {
            amount: this.gameState.gdp,
            perSecond: this.gameState.gdpPerSecond,
            totalEarned: this.gameState.totalGDPEarned
        };

        // Add all other resources
        for (const resourceType in this.gameState.resources) {
            resources[resourceType] = this.getResourceInfo(resourceType);
        }

        return resources;
    }

    /**
     * Unlock a generator (used by technology system)
     * Requirements: 7.1
     */
    unlockGenerator(type) {
        if (!this.gameState.generators[type]) {
            console.error(`Generator type ${type} not found`);
            return false;
        }

        this.gameState.generators[type].unlocked = true;
        console.log(`Generator ${type} unlocked!`);
        return true;
    }

    /**
     * Get all unlocked generators
     * Requirements: 7.1, 7.2
     */
    getUnlockedGenerators() {
        const unlockedGenerators = {};

        for (const generatorType in this.gameState.generators) {
            const generator = this.gameState.generators[generatorType];
            if (generator.unlocked) {
                unlockedGenerators[generatorType] = this.getGeneratorInfo(generatorType);
            }
        }

        return unlockedGenerators;
    }

    /**
     * Check and unlock generators based on technology research
     * Requirements: 7.1, 8.4, 8.5
     */
    checkGeneratorUnlocks() {
        // Generator unlocks are now handled by the technology system
        // This method is kept for compatibility but technology unlocks
        // are applied directly by TechnologyManager.applyTechnologyEffects()

        // The technology system handles:
        // - factory: unlocked by 'industrialization' technology
        // - school: unlocked by 'education' technology
        // - embassy: unlocked by 'diplomacy' technology
        // - bitcoinMiner: unlocked by 'cryptocurrency' technology

        // No action needed here as unlocks are handled by technology research
    }
    
    /**
     * Save game state using Orange SDK or localStorage
     * Requirements: 3.2, 3.3
     */
    saveGameState() {
        try {
            const gameState = this.getGameState();
            
            // Try to use Orange SDK if available
            if (window.GGSDK && typeof window.GGSDK.saveGameData === 'function') {
                window.GGSDK.saveGameData(gameState);
                console.log('Game state saved using Orange SDK');
            } else {
                // Use localStorage as fallback
                localStorage.setItem('pooristan_save', JSON.stringify(gameState));
                console.log('Game state saved to localStorage');
            }
            
            return true;
        } catch (error) {
            console.error('Failed to save game state:', error);
            return false;
        }
    }
    
    /**
     * Load game state from Orange SDK or localStorage
     * Requirements: 3.3, 3.4, 3.5
     */
    loadSavedGameState() {
        try {
            // Try to use Orange SDK if available
            if (window.GGSDK && typeof window.GGSDK.getGameData === 'function') {
                const defaultData = this.getDefaultGameState();
                window.GGSDK.getGameData(defaultData, (data) => {
                    this.loadGameState(data);
                    console.log('Game state loaded from Orange SDK');
                });
                return true;
            } else {
                // Use localStorage as fallback
                const savedState = localStorage.getItem('pooristan_save');
                if (savedState) {
                    const parsedState = JSON.parse(savedState);
                    this.loadGameState(parsedState);
                    console.log('Game state loaded from localStorage');
                    return true;
                }
            }
            
            // No saved state found
            console.log('No saved game state found, using default');
            return false;
        } catch (error) {
            console.error('Failed to load game state:', error);
            this.gameState = this.getDefaultGameState();
            return false;
        }
    }
    
    /**
     * Purchase multiple generators at once
     * Requirements: 2.1, 2.5
     */
    purchaseMultipleGenerators(type, quantity) {
        if (quantity <= 0) {
            return false;
        }
        
        return this.purchaseGenerator(type, quantity);
    }
    
    /**
     * Get maximum number of generators that can be purchased with current GDP
     * Requirements: 2.1, 2.5
     */
    getMaxAffordableGenerators(type) {
        if (!this.gameState.generators[type]) {
            return 0;
        }
        
        const currentGDP = this.gameState.gdp;
        if (currentGDP < this.calculateGeneratorCost(type, 1)) {
            return 0;
        }
        
        // Binary search to find maximum affordable quantity
        let low = 1;
        let high = 1000; // Reasonable upper limit
        let result = 0;
        
        while (low <= high) {
            const mid = Math.floor((low + high) / 2);
            const cost = this.calculateGeneratorCost(type, mid);
            
            if (cost <= currentGDP) {
                result = mid;
                low = mid + 1;
            } else {
                high = mid - 1;
            }
        }
        
        return result;
    }
    

}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = GameEngine;
}