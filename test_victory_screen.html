<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Victory Screen Test - Pooristan</title>
    <link rel="stylesheet" href="css/styles.css">
    <style>
        .test-container {
            max-width: 800px;
            margin: 2rem auto;
            padding: 2rem;
            background: rgba(52, 73, 94, 0.9);
            border-radius: 10px;
        }
        .test-button {
            margin: 1rem;
            padding: 1rem 2rem;
            background: #3498db;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 1rem;
        }
        .test-button:hover {
            background: #2980b9;
        }
        .test-result {
            margin: 1rem 0;
            padding: 1rem;
            border-radius: 5px;
            background: rgba(0, 0, 0, 0.3);
        }
        .test-pass {
            color: #2ecc71;
        }
        .test-fail {
            color: #e74c3c;
        }
    </style>
</head>
<body class="game-page">
    <div class="test-container">
        <h1>Victory Screen Test</h1>
        <p>This page tests the victory screen functionality for Pooristan.</p>
        
        <div class="test-controls">
            <button id="test-ai-victory-btn" class="test-button">Test AI Machine God Victory</button>
            <button id="test-influence-victory-btn" class="test-button">Test Global Influence Victory</button>
            <button id="test-score-calculation-btn" class="test-button">Test Score Calculation</button>
            <button id="test-victory-screen-btn" class="test-button">Test Victory Screen Display</button>
        </div>
        
        <div id="test-result" class="test-result">
            <p>Click a test button to run tests.</p>
        </div>
    </div>

    <!-- Victory Screen Modal (copied from index.html) -->
    <div id="victory-modal" class="modal victory-modal hidden">
        <div class="modal-content victory-content">
            <div class="victory-header">
                <h2 id="victory-title">🎉 VICTORY ACHIEVED! 🎉</h2>
                <p id="victory-subtitle">Congratulations on your triumph!</p>
            </div>
            
            <div class="victory-stats">
                <div class="victory-score">
                    <h3>Final Score</h3>
                    <div class="score-display" id="final-score">0</div>
                </div>
                
                <div class="victory-details">
                    <div class="stat-row">
                        <span class="stat-label">Victory Type:</span>
                        <span class="stat-value" id="victory-type">Unknown</span>
                    </div>
                    <div class="stat-row">
                        <span class="stat-label">Total GDP Earned:</span>
                        <span class="stat-value" id="total-gdp">$0</span>
                    </div>
                    <div class="stat-row">
                        <span class="stat-label">Play Time:</span>
                        <span class="stat-value" id="victory-play-time">00:00</span>
                    </div>
                    <div class="stat-row">
                        <span class="stat-label">Total Clicks:</span>
                        <span class="stat-value" id="victory-total-clicks">0</span>
                    </div>
                    <div class="stat-row">
                        <span class="stat-label">Technologies Researched:</span>
                        <span class="stat-value" id="technologies-count">0</span>
                    </div>
                    <div class="stat-row">
                        <span class="stat-label">Efficiency Bonus:</span>
                        <span class="stat-value" id="efficiency-bonus">+0</span>
                    </div>
                </div>
            </div>
            
            <div class="victory-actions">
                <button id="continue-playing-btn" class="victory-btn continue-btn">
                    Continue Playing
                </button>
                <button id="new-game-btn" class="victory-btn new-game-btn">
                    Start New Game
                </button>
                <button id="share-score-btn" class="victory-btn share-btn">
                    Share Score
                </button>
            </div>
        </div>
    </div>

    <!-- JavaScript Modules -->
    <script src="js/utils.js"></script>
    <script src="js/orangeSDK.js"></script>
    <script src="js/audioManager.js"></script>
    <script src="js/animationManager.js"></script>
    <script src="js/gameEngine.js"></script>
    <script src="js/technologyManager.js"></script>
    <script src="js/eventManager.js"></script>
    <script src="js/uiManager.js"></script>

    <script>
        let gameEngine, uiManager, testResult;

        document.addEventListener('DOMContentLoaded', () => {
            testResult = document.getElementById('test-result');
            
            // Initialize game components
            try {
                gameEngine = new GameEngine();
                uiManager = new UIManager(gameEngine);
                gameEngine.setUIManager(uiManager);
                
                testResult.innerHTML = '<p class="test-pass">✅ Game components initialized successfully!</p>';
            } catch (error) {
                testResult.innerHTML = `<p class="test-fail">❌ Failed to initialize game components: ${error.message}</p>`;
                return;
            }

            // Test AI Victory
            document.getElementById('test-ai-victory-btn').addEventListener('click', () => {
                try {
                    // Set up AI victory conditions
                    gameEngine.gameState.technologies.researched = ['aiDevelopment'];
                    gameEngine.gameState.totalGDPEarned = 50000;
                    gameEngine.gameState.totalPlayTime = 1800000; // 30 minutes
                    gameEngine.gameState.totalClicks = 1000;
                    
                    const victory = gameEngine.checkVictoryConditions();
                    
                    if (victory && gameEngine.gameState.victory.gameWon) {
                        testResult.innerHTML = '<p class="test-pass">✅ AI Machine God victory test passed!</p>';
                        testResult.innerHTML += `<p>Final Score: ${gameEngine.gameState.victory.finalScore}</p>`;
                    } else {
                        testResult.innerHTML = '<p class="test-fail">❌ AI victory test failed!</p>';
                    }
                } catch (error) {
                    testResult.innerHTML = `<p class="test-fail">❌ AI victory test error: ${error.message}</p>`;
                }
            });

            // Test Influence Victory
            document.getElementById('test-influence-victory-btn').addEventListener('click', () => {
                try {
                    // Reset game state
                    gameEngine.gameState = gameEngine.getDefaultGameState();
                    
                    // Set up influence victory conditions
                    gameEngine.gameState.resources.influence = 100;
                    gameEngine.gameState.totalGDPEarned = 25000;
                    gameEngine.gameState.totalPlayTime = 2400000; // 40 minutes
                    gameEngine.gameState.totalClicks = 500;
                    
                    const victory = gameEngine.checkVictoryConditions();
                    
                    if (victory && gameEngine.gameState.victory.gameWon) {
                        testResult.innerHTML = '<p class="test-pass">✅ Global Influence victory test passed!</p>';
                        testResult.innerHTML += `<p>Final Score: ${gameEngine.gameState.victory.finalScore}</p>`;
                    } else {
                        testResult.innerHTML = '<p class="test-fail">❌ Influence victory test failed!</p>';
                    }
                } catch (error) {
                    testResult.innerHTML = `<p class="test-fail">❌ Influence victory test error: ${error.message}</p>`;
                }
            });

            // Test Score Calculation
            document.getElementById('test-score-calculation-btn').addEventListener('click', () => {
                try {
                    // Set up test conditions
                    gameEngine.gameState.totalGDPEarned = 10000;
                    gameEngine.gameState.totalPlayTime = 1200000; // 20 minutes
                    gameEngine.gameState.totalClicks = 200;
                    gameEngine.gameState.technologies.researched = ['basicAgriculture', 'industrialization'];
                    gameEngine.gameState.resources = { food: 100, industry: 50, knowledge: 25, influence: 10, bitcoin: 5 };
                    gameEngine.gameState.generators = {
                        farm: { count: 5 },
                        factory: { count: 3 },
                        school: { count: 0 },
                        embassy: { count: 0 },
                        bitcoinMiner: { count: 0 }
                    };
                    
                    const score = gameEngine.calculateFinalScore();
                    
                    if (typeof score === 'number' && score > 0) {
                        testResult.innerHTML = '<p class="test-pass">✅ Score calculation test passed!</p>';
                        testResult.innerHTML += `<p>Calculated Score: ${score}</p>`;
                        testResult.innerHTML += `<p>Base GDP: ${gameEngine.gameState.totalGDPEarned}</p>`;
                        testResult.innerHTML += `<p>Play Time: ${gameEngine.gameState.totalPlayTime / 60000} minutes</p>`;
                    } else {
                        testResult.innerHTML = '<p class="test-fail">❌ Score calculation test failed!</p>';
                    }
                } catch (error) {
                    testResult.innerHTML = `<p class="test-fail">❌ Score calculation test error: ${error.message}</p>`;
                }
            });

            // Test Victory Screen Display
            document.getElementById('test-victory-screen-btn').addEventListener('click', () => {
                try {
                    // Set up victory conditions
                    gameEngine.gameState.victory.gameWon = true;
                    gameEngine.gameState.victory.finalScore = 75000;
                    gameEngine.gameState.technologies.researched = ['aiDevelopment'];
                    gameEngine.gameState.totalGDPEarned = 50000;
                    gameEngine.gameState.totalPlayTime = 1800000;
                    gameEngine.gameState.totalClicks = 800;
                    
                    // Show victory screen
                    uiManager.showVictoryScreen({
                        type: 'aiDevelopment',
                        score: 75000
                    });
                    
                    testResult.innerHTML = '<p class="test-pass">✅ Victory screen display test started!</p>';
                    testResult.innerHTML += '<p>Victory screen should be visible now. Check the modal display.</p>';
                } catch (error) {
                    testResult.innerHTML = `<p class="test-fail">❌ Victory screen test error: ${error.message}</p>`;
                }
            });
        });
    </script>
</body>
</html>
