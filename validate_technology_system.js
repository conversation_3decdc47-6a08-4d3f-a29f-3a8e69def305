/**
 * Technology System Validation Script
 * Run with: node validate_technology_system.js
 */

// Mock DOM utilities for Node.js testing
global.DOMUtils = {
    getElementById: () => null,
    querySelector: () => null,
    setText: () => {},
    setHTML: () => {},
    addClass: () => {}
};

global.NumberFormatter = {
    format: (num) => num.toString()
};

global.TimeUtils = {
    formatTime: (ms) => Math.floor(ms / 1000) + 's'
};

global.StorageUtils = {
    save: () => {},
    load: () => null
};

global.GAME_CONFIG = {
    VERSION: "1.0.0",
    RESOURCE_TYPES: {
        GDP: 'gdp',
        FOOD: 'food',
        INDUSTRY: 'industry',
        KNOWLEDGE: 'knowledge',
        INFLUENCE: 'influence',
        BITCOIN: 'bitcoin'
    }
};

// Load the game modules
const fs = require('fs');
const path = require('path');

// Read and evaluate the game files
const gameEngineCode = fs.readFileSync(path.join(__dirname, 'js/gameEngine.js'), 'utf8');
const technologyManagerCode = fs.readFileSync(path.join(__dirname, 'js/technologyManager.js'), 'utf8');

eval(gameEngineCode);
eval(technologyManagerCode);

// Validation tests
function runValidationTests() {
    console.log('🧪 Running Technology System Validation Tests...\n');
    
    let passed = 0;
    let total = 0;
    
    function test(description, testFn) {
        total++;
        try {
            const result = testFn();
            if (result) {
                console.log(`✅ ${description}`);
                passed++;
            } else {
                console.log(`❌ ${description}`);
            }
        } catch (error) {
            console.log(`❌ ${description} - Error: ${error.message}`);
        }
    }
    
    // Initialize game components
    const gameEngine = new GameEngine();
    const technologyManager = new TechnologyManager(gameEngine);
    gameEngine.setTechnologyManager(technologyManager);
    
    // Test 1: Basic initialization
    test('TechnologyManager initializes correctly', () => {
        return technologyManager && typeof technologyManager.technologyTree === 'object';
    });
    
    // Test 2: Technology tree structure
    test('Technology tree contains all required technologies', () => {
        const tree = technologyManager.technologyTree;
        const requiredTechs = [
            'basicAgriculture', 'industrialization', 'education',
            'irrigation', 'massProduction', 'diplomacy',
            'cryptocurrency', 'aiDevelopment'
        ];
        return requiredTechs.every(tech => tree[tech]);
    });
    
    // Test 3: Technology prerequisites
    test('Technology prerequisites are correctly defined', () => {
        const tree = technologyManager.technologyTree;
        return tree.irrigation.prerequisites.includes('basicAgriculture') &&
               tree.aiDevelopment.prerequisites.includes('cryptocurrency') &&
               tree.aiDevelopment.prerequisites.includes('globalInfluence');
    });
    
    // Test 4: Technology costs
    test('Technologies have valid cost structures', () => {
        const tree = technologyManager.technologyTree;
        return tree.basicAgriculture.cost.knowledge === 10 &&
               tree.industrialization.cost.knowledge === 25 &&
               tree.aiDevelopment.cost.knowledge === 10000;
    });
    
    // Test 5: Technology effects
    test('Technologies have correct effect definitions', () => {
        const tree = technologyManager.technologyTree;
        return tree.basicAgriculture.effects.generatorMultiplier.farm === 1.25 &&
               tree.industrialization.effects.unlockGenerator === 'factory' &&
               tree.aiDevelopment.effects.victoryCondition === true;
    });
    
    // Test 6: Available technologies calculation
    test('Available technologies are calculated correctly', () => {
        const available = technologyManager.getAvailableTechnologies();
        const availableIds = available.map(tech => tech.id);
        return availableIds.includes('basicAgriculture') &&
               availableIds.includes('industrialization') &&
               availableIds.includes('education');
    });
    
    // Test 7: Research cost checking
    test('Research cost checking works correctly', () => {
        // Give enough resources
        gameEngine.gameState.resources.knowledge = 100;
        return technologyManager.canAffordResearch({ knowledge: 10 }) &&
               !technologyManager.canAffordResearch({ knowledge: 200 });
    });
    
    // Test 8: Research prerequisites checking
    test('Research prerequisites checking works correctly', () => {
        return technologyManager.checkPrerequisites('basicAgriculture') &&
               !technologyManager.checkPrerequisites('irrigation');
    });
    
    // Test 9: Research functionality
    test('Research can be started and completed', () => {
        gameEngine.gameState.resources.knowledge = 100;
        const started = technologyManager.startResearch('basicAgriculture');
        if (!started) return false;
        
        // Simulate completion
        gameEngine.gameState.technologies.researchProgress = 1.0;
        const completed = technologyManager.completeResearch('basicAgriculture');
        return completed && gameEngine.gameState.technologies.researched.includes('basicAgriculture');
    });
    
    // Test 10: Technology effects application
    test('Technology effects are applied correctly', () => {
        const farmGenerator = gameEngine.gameState.generators.farm;
        return farmGenerator.technologyMultipliers &&
               farmGenerator.technologyMultipliers.basicAgriculture === 1.25;
    });
    
    // Test 11: Production calculation with technology
    test('Production calculation includes technology multipliers', () => {
        gameEngine.gameState.generators.farm.count = 1;
        const production = gameEngine.calculateGeneratorProduction('farm');
        // Should be base(1) * count(1) * upgrade(1) * tech(1.25) * global(1) = 1.25
        return Math.abs(production - 1.25) < 0.01;
    });
    
    // Test 12: Generator unlocking
    test('Generator unlocking through technology works', () => {
        // Research industrialization to unlock factory
        gameEngine.gameState.resources.knowledge = 100;
        technologyManager.startResearch('industrialization');
        gameEngine.gameState.technologies.researchProgress = 1.0;
        technologyManager.completeResearch('industrialization');
        
        return gameEngine.gameState.generators.factory.unlocked;
    });
    
    // Test 13: Technology tree display data
    test('Technology tree display data is correct', () => {
        const displayData = technologyManager.getTechnologyTreeForDisplay();
        const basicAg = displayData.basicAgriculture;
        return basicAg.isResearched && !basicAg.canResearch;
    });
    
    // Test 14: Victory conditions
    test('Victory conditions work correctly', () => {
        // Test AI Development victory
        gameEngine.gameState.technologies.researched.push('aiDevelopment');
        const victory = gameEngine.checkVictoryConditions();
        return victory && gameEngine.gameState.victory.gameWon;
    });
    
    // Test 15: Final score calculation
    test('Final score calculation works', () => {
        const score = gameEngine.calculateFinalScore();
        return typeof score === 'number' && score > 0;
    });
    
    console.log(`\n📊 Test Results: ${passed}/${total} tests passed`);
    
    if (passed === total) {
        console.log('🎉 All tests passed! Technology system is working correctly.');
        return true;
    } else {
        console.log('⚠️  Some tests failed. Please review the implementation.');
        return false;
    }
}

// Run the validation
if (require.main === module) {
    runValidationTests();
}

module.exports = { runValidationTests };
