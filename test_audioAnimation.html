<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Audio and Animation Test - Pooristan</title>
    <link rel="stylesheet" href="css/styles.css">
    <style>
        .test-container {
            max-width: 800px;
            margin: 2rem auto;
            padding: 2rem;
            background: rgba(52, 73, 94, 0.9);
            border-radius: 10px;
        }
        
        .test-section {
            margin-bottom: 2rem;
            padding: 1rem;
            background: rgba(44, 62, 80, 0.6);
            border-radius: 8px;
        }
        
        .test-button {
            margin: 0.5rem;
            padding: 0.75rem 1.5rem;
            background: linear-gradient(45deg, #3498db, #2980b9);
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        }
        
        .test-target {
            width: 100px;
            height: 100px;
            background: linear-gradient(45deg, #f39c12, #e67e22);
            border-radius: 10px;
            margin: 1rem auto;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            position: relative;
        }
        
        .settings-panel {
            background: rgba(44, 62, 80, 0.8);
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1rem;
        }
        
        .setting-row {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 0.5rem;
        }
        
        .setting-row label {
            color: #ecf0f1;
        }
        
        .setting-row input {
            background: rgba(52, 73, 94, 0.8);
            border: 1px solid #7f8c8d;
            border-radius: 4px;
            padding: 0.25rem 0.5rem;
            color: #ecf0f1;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 style="color: #f39c12; text-align: center; margin-bottom: 2rem;">
            Audio and Animation System Test
        </h1>
        
        <!-- Settings Panel -->
        <div class="settings-panel">
            <h3 style="color: #f39c12; margin-bottom: 1rem;">Settings</h3>
            <div class="setting-row">
                <label>Audio Volume:</label>
                <input type="range" id="volumeSlider" min="0" max="1" step="0.1" value="0.7">
                <span id="volumeDisplay">70%</span>
            </div>
            <div class="setting-row">
                <label>Animation Speed:</label>
                <input type="range" id="speedSlider" min="0.1" max="3" step="0.1" value="1">
                <span id="speedDisplay">1.0x</span>
            </div>
            <div class="setting-row">
                <label>Audio Enabled:</label>
                <input type="checkbox" id="audioToggle" checked>
            </div>
            <div class="setting-row">
                <label>Animations Enabled:</label>
                <input type="checkbox" id="animationToggle" checked>
            </div>
        </div>
        
        <!-- Audio Tests -->
        <div class="test-section">
            <h3 style="color: #f39c12;">Audio Tests</h3>
            <p style="color: #bdc3c7; margin-bottom: 1rem;">
                Test different sound effects (Note: Audio files are placeholders)
            </p>
            <button class="test-button" onclick="testAudio('click')">Click Sound</button>
            <button class="test-button" onclick="testAudio('purchase')">Purchase Sound</button>
            <button class="test-button" onclick="testAudio('upgrade')">Upgrade Sound</button>
            <button class="test-button" onclick="testAudio('event')">Event Sound</button>
            <button class="test-button" onclick="testAudio('victory')">Victory Sound</button>
            <button class="test-button" onclick="testAudio('error')">Error Sound</button>
        </div>
        
        <!-- Animation Tests -->
        <div class="test-section">
            <h3 style="color: #f39c12;">Animation Tests</h3>
            <p style="color: #bdc3c7; margin-bottom: 1rem;">
                Test different visual animations and effects
            </p>
            <div class="test-target" id="testTarget">Target</div>
            <button class="test-button" onclick="testAnimation('resourceGain')">Resource Gain</button>
            <button class="test-button" onclick="testAnimation('purchase')">Purchase Animation</button>
            <button class="test-button" onclick="testAnimation('upgrade')">Upgrade Animation</button>
            <button class="test-button" onclick="testAnimation('event')">Event Animation</button>
            <button class="test-button" onclick="testAnimation('milestone')">Milestone Celebration</button>
        </div>
        
        <!-- Combined Tests -->
        <div class="test-section">
            <h3 style="color: #f39c12;">Combined Audio + Animation Tests</h3>
            <p style="color: #bdc3c7; margin-bottom: 1rem;">
                Test audio and animation together
            </p>
            <button class="test-button" onclick="testCombined('click')">Click Feedback</button>
            <button class="test-button" onclick="testCombined('purchase')">Purchase Feedback</button>
            <button class="test-button" onclick="testCombined('upgrade')">Upgrade Feedback</button>
            <button class="test-button" onclick="testCombined('victory')">Victory Celebration</button>
        </div>
        
        <!-- Status Display -->
        <div class="test-section">
            <h3 style="color: #f39c12;">System Status</h3>
            <div id="statusDisplay" style="color: #bdc3c7;">
                Initializing...
            </div>
        </div>
    </div>

    <!-- Include JavaScript modules -->
    <script src="js/utils.js"></script>
    <script src="js/audioManager.js"></script>
    <script src="js/animationManager.js"></script>
    
    <script>
        let audioManager;
        let animationManager;
        
        // Initialize managers
        document.addEventListener('DOMContentLoaded', async () => {
            try {
                audioManager = new AudioManager();
                animationManager = new AnimationManager();
                
                // Setup event listeners
                setupEventListeners();
                
                // Update status
                updateStatus();
                
                console.log('Test page initialized successfully');
            } catch (error) {
                console.error('Failed to initialize test page:', error);
                document.getElementById('statusDisplay').innerHTML = 
                    `<span style="color: #e74c3c;">Error: ${error.message}</span>`;
            }
        });
        
        function setupEventListeners() {
            // Volume control
            const volumeSlider = document.getElementById('volumeSlider');
            const volumeDisplay = document.getElementById('volumeDisplay');
            volumeSlider.addEventListener('input', (e) => {
                const volume = parseFloat(e.target.value);
                audioManager.setVolume(volume);
                volumeDisplay.textContent = Math.round(volume * 100) + '%';
            });
            
            // Speed control
            const speedSlider = document.getElementById('speedSlider');
            const speedDisplay = document.getElementById('speedDisplay');
            speedSlider.addEventListener('input', (e) => {
                const speed = parseFloat(e.target.value);
                animationManager.setAnimationSpeed(speed);
                speedDisplay.textContent = speed.toFixed(1) + 'x';
            });
            
            // Audio toggle
            const audioToggle = document.getElementById('audioToggle');
            audioToggle.addEventListener('change', (e) => {
                audioManager.setEnabled(e.target.checked);
                updateStatus();
            });
            
            // Animation toggle
            const animationToggle = document.getElementById('animationToggle');
            animationToggle.addEventListener('change', (e) => {
                animationManager.setEnabled(e.target.checked);
                updateStatus();
            });
        }
        
        function testAudio(soundType) {
            if (!audioManager) return;
            
            switch (soundType) {
                case 'click':
                    audioManager.playClickSound();
                    break;
                case 'purchase':
                    audioManager.playPurchaseSound();
                    break;
                case 'upgrade':
                    audioManager.playUpgradeSound();
                    break;
                case 'event':
                    audioManager.playEventSound();
                    break;
                case 'victory':
                    audioManager.playVictorySound();
                    break;
                case 'error':
                    audioManager.playEventSound('error');
                    break;
            }
            
            console.log(`Played ${soundType} sound`);
        }
        
        function testAnimation(animationType) {
            if (!animationManager) return;
            
            const target = document.getElementById('testTarget');
            
            switch (animationType) {
                case 'resourceGain':
                    animationManager.animateResourceGain('gdp', 100, target);
                    break;
                case 'purchase':
                    animationManager.animatePurchase('farm');
                    target.classList.add('animation-glow');
                    setTimeout(() => target.classList.remove('animation-glow'), 500);
                    break;
                case 'upgrade':
                    animationManager.animateUpgrade('farm');
                    target.style.animation = 'upgrade-spin 0.5s ease-out';
                    setTimeout(() => target.style.animation = '', 500);
                    break;
                case 'event':
                    animationManager.animateEvent('crisis');
                    target.style.animation = 'shake 0.5s ease-in-out';
                    setTimeout(() => target.style.animation = '', 500);
                    break;
                case 'milestone':
                    animationManager.animateMilestone('achievement', target);
                    break;
            }
            
            console.log(`Played ${animationType} animation`);
        }
        
        function testCombined(type) {
            testAudio(type);
            testAnimation(type === 'click' ? 'resourceGain' : type);
        }
        
        function updateStatus() {
            const audioSettings = audioManager ? audioManager.getSettings() : null;
            const animationSettings = animationManager ? animationManager.getSettings() : null;
            
            const status = `
                <strong>Audio Manager:</strong><br>
                - Enabled: ${audioSettings ? audioSettings.isEnabled : 'N/A'}<br>
                - Volume: ${audioSettings ? Math.round(audioSettings.volume * 100) + '%' : 'N/A'}<br>
                - Muted: ${audioSettings ? audioSettings.isMuted : 'N/A'}<br>
                - Sounds Loaded: ${audioSettings ? audioSettings.soundsLoaded : 'N/A'}<br><br>
                
                <strong>Animation Manager:</strong><br>
                - Enabled: ${animationSettings ? animationSettings.isEnabled : 'N/A'}<br>
                - Speed: ${animationSettings ? animationSettings.animationSpeed.toFixed(1) + 'x' : 'N/A'}<br>
                - Active Animations: ${animationSettings ? animationSettings.activeAnimations : 'N/A'}
            `;
            
            document.getElementById('statusDisplay').innerHTML = status;
        }
        
        // Update status every 2 seconds
        setInterval(updateStatus, 2000);
    </script>
</body>
</html>
