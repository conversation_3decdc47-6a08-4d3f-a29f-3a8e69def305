<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pause/Resume Functionality Test - Pooristan</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f0f0f0;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .test-pass {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .test-fail {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .test-info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            background-color: #007bff;
            color: white;
        }
        button:hover {
            background-color: #0056b3;
        }
        .status {
            font-weight: bold;
            margin: 10px 0;
        }
        .metrics {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin: 10px 0;
        }
        .metric {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            border-left: 4px solid #007bff;
        }
    </style>
</head>
<body>
    <h1>Pause/Resume Functionality Test</h1>
    
    <div class="test-container">
        <h2>Game Engine Status</h2>
        <div id="gameStatus" class="status">Initializing...</div>
        
        <div class="metrics">
            <div class="metric">
                <strong>Game Running:</strong> <span id="isRunning">false</span>
            </div>
            <div class="metric">
                <strong>Game Paused:</strong> <span id="isPaused">false</span>
            </div>
            <div class="metric">
                <strong>Total Play Time:</strong> <span id="totalPlayTime">0</span>ms
            </div>
            <div class="metric">
                <strong>Total Paused Time:</strong> <span id="totalPausedTime">0</span>ms
            </div>
            <div class="metric">
                <strong>GDP:</strong> <span id="gdp">0</span>
            </div>
            <div class="metric">
                <strong>GDP/sec:</strong> <span id="gdpPerSec">0</span>
            </div>
        </div>
        
        <div>
            <button onclick="startGame()">Start Game</button>
            <button onclick="pauseGame()">Pause Game</button>
            <button onclick="resumeGame()">Resume Game</button>
            <button onclick="quitGame()">Quit Game</button>
            <button onclick="clickGDP()">Click GDP (+1)</button>
            <button onclick="buyFarm()">Buy Farm</button>
        </div>
    </div>
    
    <div class="test-container">
        <h2>Test Results</h2>
        <div id="testResults"></div>
        <button onclick="runAllTests()">Run All Tests</button>
    </div>

    <!-- Include game scripts -->
    <script src="js/utils.js"></script>
    <script src="js/orangeSDK.js"></script>
    <script src="js/gameEngine.js"></script>
    <script src="js/technologyManager.js"></script>
    <script src="js/eventManager.js"></script>

    <script>
        let gameEngine;
        let orangeSDK;
        let updateInterval;

        // Initialize game components
        function initializeGame() {
            try {
                orangeSDK = new OrangeSDKManager();
                gameEngine = new GameEngine(orangeSDK);
                
                // Initialize other managers
                const technologyManager = new TechnologyManager(gameEngine);
                const eventManager = new EventManager(gameEngine);
                
                gameEngine.setTechnologyManager(technologyManager);
                gameEngine.setEventManager(eventManager);
                
                updateStatus();
                logResult('Game components initialized successfully', 'pass');
                
                // Start updating display
                updateInterval = setInterval(updateStatus, 100);
                
            } catch (error) {
                logResult(`Failed to initialize game: ${error.message}`, 'fail');
            }
        }

        function updateStatus() {
            if (!gameEngine) return;
            
            document.getElementById('isRunning').textContent = gameEngine.isRunning;
            document.getElementById('isPaused').textContent = gameEngine.isPaused;
            document.getElementById('totalPlayTime').textContent = Math.round(gameEngine.gameState.totalPlayTime);
            document.getElementById('totalPausedTime').textContent = Math.round(gameEngine.gameState.totalPausedTime || 0);
            document.getElementById('gdp').textContent = Math.round(gameEngine.gameState.gdp);
            document.getElementById('gdpPerSec').textContent = gameEngine.gameState.gdpPerSecond.toFixed(2);
            
            let status = 'Stopped';
            if (gameEngine.isRunning && gameEngine.isPaused) {
                status = 'Paused';
            } else if (gameEngine.isRunning) {
                status = 'Running';
            }
            document.getElementById('gameStatus').textContent = status;
        }

        function startGame() {
            if (gameEngine) {
                gameEngine.startGameLoop();
                logResult('Game started', 'info');
            }
        }

        function pauseGame() {
            if (gameEngine) {
                gameEngine.pauseGame();
                logResult('Game paused', 'info');
            }
        }

        function resumeGame() {
            if (gameEngine) {
                gameEngine.resumeGame();
                logResult('Game resumed', 'info');
            }
        }

        function quitGame() {
            if (gameEngine) {
                gameEngine.quitGame();
                logResult('Game quit', 'info');
            }
        }

        function clickGDP() {
            if (gameEngine) {
                gameEngine.clickGDP();
                logResult('GDP clicked', 'info');
            }
        }

        function buyFarm() {
            if (gameEngine && gameEngine.canAfford(10)) {
                gameEngine.purchaseGenerator('farm');
                logResult('Farm purchased', 'info');
            } else {
                logResult('Cannot afford farm (need 10 GDP)', 'fail');
            }
        }

        function logResult(message, type) {
            const resultsDiv = document.getElementById('testResults');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result test-${type}`;
            resultDiv.textContent = `${new Date().toLocaleTimeString()}: ${message}`;
            resultsDiv.appendChild(resultDiv);
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        }

        async function runAllTests() {
            logResult('Starting pause/resume tests...', 'info');
            
            try {
                // Test 1: Basic pause/resume functionality
                await testBasicPauseResume();
                
                // Test 2: Timing accuracy
                await testTimingAccuracy();
                
                // Test 3: State persistence
                await testStatePersistence();
                
                logResult('All tests completed!', 'pass');
            } catch (error) {
                logResult(`Test suite failed: ${error.message}`, 'fail');
            }
        }

        async function testBasicPauseResume() {
            logResult('Test 1: Basic pause/resume functionality', 'info');
            
            // Start the game
            gameEngine.startGameLoop();
            await sleep(100);
            
            if (!gameEngine.isRunning) {
                throw new Error('Game should be running');
            }
            logResult('✓ Game starts correctly', 'pass');
            
            // Pause the game
            gameEngine.pauseGame();
            await sleep(50);
            
            if (!gameEngine.isPaused) {
                throw new Error('Game should be paused');
            }
            logResult('✓ Game pauses correctly', 'pass');
            
            // Resume the game
            gameEngine.resumeGame();
            await sleep(50);
            
            if (gameEngine.isPaused) {
                throw new Error('Game should not be paused after resume');
            }
            logResult('✓ Game resumes correctly', 'pass');
        }

        async function testTimingAccuracy() {
            logResult('Test 2: Timing accuracy during pause/resume', 'info');
            
            // Reset and start fresh
            gameEngine.resetGame();
            gameEngine.startGameLoop();
            
            const initialPlayTime = gameEngine.gameState.totalPlayTime;
            await sleep(200);
            
            const playTimeBeforePause = gameEngine.gameState.totalPlayTime;
            const playTimeDelta1 = playTimeBeforePause - initialPlayTime;
            
            // Pause for a significant time
            gameEngine.pauseGame();
            await sleep(300);
            
            const playTimeAfterPause = gameEngine.gameState.totalPlayTime;
            
            // Play time should not increase during pause
            if (playTimeAfterPause !== playTimeBeforePause) {
                throw new Error(`Play time increased during pause: ${playTimeBeforePause} -> ${playTimeAfterPause}`);
            }
            logResult('✓ Play time does not increase during pause', 'pass');
            
            // Resume and check timing continues
            gameEngine.resumeGame();
            await sleep(200);
            
            const playTimeAfterResume = gameEngine.gameState.totalPlayTime;
            const playTimeDelta2 = playTimeAfterResume - playTimeAfterPause;
            
            // Should have similar timing deltas (within reasonable tolerance)
            const tolerance = 50; // 50ms tolerance
            if (Math.abs(playTimeDelta1 - playTimeDelta2) > tolerance) {
                logResult(`⚠ Timing delta difference: ${Math.abs(playTimeDelta1 - playTimeDelta2)}ms (tolerance: ${tolerance}ms)`, 'info');
            } else {
                logResult('✓ Timing accuracy maintained across pause/resume', 'pass');
            }
        }

        async function testStatePersistence() {
            logResult('Test 3: State persistence during pause/resume', 'info');
            
            // Set up some game state
            gameEngine.resetGame();
            gameEngine.startGameLoop();
            
            // Add some GDP and buy a generator
            for (let i = 0; i < 15; i++) {
                gameEngine.clickGDP();
            }
            gameEngine.purchaseGenerator('farm');
            
            const gdpBeforePause = gameEngine.gameState.gdp;
            const farmCountBeforePause = gameEngine.gameState.generators.farm.count;
            
            // Pause and resume
            gameEngine.pauseGame();
            await sleep(100);
            gameEngine.resumeGame();
            await sleep(100);
            
            const gdpAfterResume = gameEngine.gameState.gdp;
            const farmCountAfterResume = gameEngine.gameState.generators.farm.count;
            
            // State should be preserved (GDP might increase slightly due to generation)
            if (farmCountAfterResume !== farmCountBeforePause) {
                throw new Error(`Farm count changed: ${farmCountBeforePause} -> ${farmCountAfterResume}`);
            }
            logResult('✓ Generator state preserved during pause/resume', 'pass');
            
            if (gdpAfterResume < gdpBeforePause) {
                throw new Error(`GDP decreased: ${gdpBeforePause} -> ${gdpAfterResume}`);
            }
            logResult('✓ GDP state preserved and continues generating', 'pass');
        }

        function sleep(ms) {
            return new Promise(resolve => setTimeout(resolve, ms));
        }

        // Initialize when page loads
        window.addEventListener('load', initializeGame);
        
        // Cleanup on page unload
        window.addEventListener('beforeunload', () => {
            if (updateInterval) {
                clearInterval(updateInterval);
            }
            if (gameEngine) {
                gameEngine.quitGame();
            }
        });
    </script>
</body>
</html>
