<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Event System Test - Pooristan</title>
    <link rel="stylesheet" href="css/styles.css">
    <style>
        .test-container {
            max-width: 800px;
            margin: 2rem auto;
            padding: 2rem;
            background: rgba(52, 73, 94, 0.9);
            border-radius: 10px;
        }
        .test-section {
            margin-bottom: 2rem;
            padding: 1rem;
            background: rgba(44, 62, 80, 0.6);
            border-radius: 8px;
        }
        .test-button {
            padding: 0.5rem 1rem;
            margin: 0.5rem;
            background: #3498db;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
        .test-button:hover {
            background: #2980b9;
        }
        .test-log {
            background: #2c3e50;
            padding: 1rem;
            border-radius: 5px;
            font-family: monospace;
            font-size: 0.9rem;
            max-height: 300px;
            overflow-y: auto;
            margin-top: 1rem;
        }
        .game-state {
            background: #34495e;
            padding: 1rem;
            border-radius: 5px;
            margin-top: 1rem;
        }
        .resource-display {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 1rem;
            margin-top: 1rem;
        }
        .resource-item {
            background: rgba(44, 62, 80, 0.6);
            padding: 0.5rem;
            border-radius: 5px;
            text-align: center;
        }
    </style>
</head>
<body class="game-page">
    <div class="test-container">
        <h1>Event System Test</h1>
        
        <div class="test-section">
            <h3>Game State</h3>
            <div class="resource-display">
                <div class="resource-item">
                    <strong>GDP:</strong> $<span id="gdp-display">0</span>
                </div>
                <div class="resource-item">
                    <strong>Food:</strong> <span id="food-display">0</span>
                </div>
                <div class="resource-item">
                    <strong>Industry:</strong> <span id="industry-display">0</span>
                </div>
                <div class="resource-item">
                    <strong>Knowledge:</strong> <span id="knowledge-display">0</span>
                </div>
                <div class="resource-item">
                    <strong>Influence:</strong> <span id="influence-display">0</span>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3>Event Testing</h3>
            <button class="test-button" onclick="triggerDrought()">Trigger Drought</button>
            <button class="test-button" onclick="triggerCivilUnrest()">Trigger Civil Unrest</button>
            <button class="test-button" onclick="triggerTradeDeal()">Trigger Trade Deal</button>
            <button class="test-button" onclick="triggerIMFLoan()">Trigger IMF Loan</button>
            <button class="test-button" onclick="addResources()">Add Resources</button>
            <button class="test-button" onclick="clearLog()">Clear Log</button>
        </div>

        <div class="test-section">
            <h3>Active Events</h3>
            <div id="active-events">No active events</div>
        </div>

        <div class="test-section">
            <h3>Test Log</h3>
            <div id="test-log" class="test-log"></div>
        </div>
    </div>

    <!-- Event Modal -->
    <div id="event-modal" class="modal hidden">
        <div class="modal-content">
            <h3 id="event-title"></h3>
            <p id="event-description"></p>
            <div id="event-choices" class="event-choices">
                <!-- Event choices will be populated by JavaScript -->
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="js/utils.js"></script>
    <script src="js/gameEngine.js"></script>
    <script src="js/eventManager.js"></script>
    <script src="js/uiManager.js"></script>

    <script>
        let gameEngine;
        let eventManager;
        let uiManager;

        function log(message, type = 'info') {
            const logElement = document.getElementById('test-log');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.style.color = type === 'error' ? '#e74c3c' : type === 'success' ? '#2ecc71' : '#ecf0f1';
            logEntry.textContent = `[${timestamp}] ${message}`;
            logElement.appendChild(logEntry);
            logElement.scrollTop = logElement.scrollHeight;
        }

        function updateDisplays() {
            if (!gameEngine) return;
            
            const gameState = gameEngine.getGameState();
            document.getElementById('gdp-display').textContent = Math.floor(gameState.gdp);
            document.getElementById('food-display').textContent = Math.floor(gameState.resources.food);
            document.getElementById('industry-display').textContent = Math.floor(gameState.resources.industry);
            document.getElementById('knowledge-display').textContent = Math.floor(gameState.resources.knowledge);
            document.getElementById('influence-display').textContent = Math.floor(gameState.resources.influence);

            // Update active events
            const activeEvents = eventManager.getActiveEvents();
            const activeEventsElement = document.getElementById('active-events');
            if (activeEvents.length === 0) {
                activeEventsElement.textContent = 'No active events';
            } else {
                activeEventsElement.innerHTML = activeEvents.map(event => 
                    `<div style="margin: 0.5rem 0; padding: 0.5rem; background: rgba(231, 76, 60, 0.2); border-radius: 5px;">
                        <strong>${event.name}</strong><br>
                        <small>Started: ${new Date(event.startTime).toLocaleTimeString()}</small>
                    </div>`
                ).join('');
            }
        }

        function initializeTest() {
            try {
                log('Initializing event system test...', 'info');
                
                // Create game engine
                gameEngine = new GameEngine();
                log('✓ GameEngine created', 'success');
                
                // Create UI manager
                uiManager = new UIManager(gameEngine);
                uiManager.cacheElements();
                log('✓ UIManager created', 'success');
                
                // Create event manager
                eventManager = new EventManager(gameEngine, uiManager);
                log('✓ EventManager created', 'success');
                
                // Connect modules
                gameEngine.setEventManager(eventManager);
                gameEngine.setUIManager(uiManager);
                
                // Give player some initial resources
                gameEngine.gameState.gdp = 1000;
                gameEngine.gameState.resources.food = 50;
                gameEngine.gameState.resources.industry = 100;
                gameEngine.gameState.resources.knowledge = 25;
                gameEngine.gameState.resources.influence = 10;
                
                updateDisplays();
                log('✓ Test environment initialized successfully', 'success');
                
            } catch (error) {
                log(`✗ Initialization failed: ${error.message}`, 'error');
                console.error('Initialization error:', error);
            }
        }

        function triggerDrought() {
            try {
                log('Triggering drought event...', 'info');
                const droughtConfig = eventManager.eventConfigs.localCrisis.drought;
                eventManager.triggerLocalCrisis('drought', droughtConfig);
                updateDisplays();
                log('✓ Drought event triggered', 'success');
            } catch (error) {
                log(`✗ Failed to trigger drought: ${error.message}`, 'error');
            }
        }

        function triggerCivilUnrest() {
            try {
                log('Triggering civil unrest event...', 'info');
                const unrestConfig = eventManager.eventConfigs.localCrisis.civilUnrest;
                eventManager.triggerLocalCrisis('civilUnrest', unrestConfig);
                updateDisplays();
                log('✓ Civil unrest event triggered', 'success');
            } catch (error) {
                log(`✗ Failed to trigger civil unrest: ${error.message}`, 'error');
            }
        }

        function triggerTradeDeal() {
            try {
                log('Triggering trade deal event...', 'info');
                const tradeConfig = eventManager.eventConfigs.internationalPolitics.tradeDeal;
                eventManager.triggerInternationalEvent('tradeDeal', tradeConfig);
                updateDisplays();
                log('✓ Trade deal event triggered', 'success');
            } catch (error) {
                log(`✗ Failed to trigger trade deal: ${error.message}`, 'error');
            }
        }

        function triggerIMFLoan() {
            try {
                log('Triggering IMF loan event...', 'info');
                eventManager.triggerIMFLoan();
                updateDisplays();
                log('✓ IMF loan event triggered', 'success');
            } catch (error) {
                log(`✗ Failed to trigger IMF loan: ${error.message}`, 'error');
            }
        }

        function addResources() {
            gameEngine.gameState.gdp += 1000;
            gameEngine.gameState.resources.food += 100;
            gameEngine.gameState.resources.industry += 200;
            gameEngine.gameState.resources.knowledge += 50;
            gameEngine.gameState.resources.influence += 20;
            updateDisplays();
            log('✓ Resources added', 'success');
        }

        function clearLog() {
            document.getElementById('test-log').innerHTML = '';
        }

        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', () => {
            initializeTest();
            
            // Update displays every second
            setInterval(updateDisplays, 1000);
        });
    </script>
</body>
</html>
