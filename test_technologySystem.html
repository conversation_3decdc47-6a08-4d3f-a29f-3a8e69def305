<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Technology System Test - Pooristan</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #2c3e50;
            color: #ecf0f1;
        }
        .test-section {
            background: rgba(44, 62, 80, 0.8);
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            border: 1px solid #34495e;
        }
        .test-result {
            padding: 10px;
            margin: 5px 0;
            border-radius: 4px;
        }
        .test-pass {
            background: rgba(46, 204, 113, 0.2);
            border-left: 4px solid #2ecc71;
        }
        .test-fail {
            background: rgba(231, 76, 60, 0.2);
            border-left: 4px solid #e74c3c;
        }
        .test-info {
            background: rgba(52, 152, 219, 0.2);
            border-left: 4px solid #3498db;
        }
        button {
            background: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #2980b9;
        }
        .tech-tree-display {
            max-height: 400px;
            overflow-y: auto;
            background: rgba(52, 73, 94, 0.5);
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .tech-item {
            padding: 5px;
            margin: 2px 0;
            border-radius: 3px;
            font-size: 0.9rem;
        }
        .tech-researched { background: rgba(46, 204, 113, 0.3); }
        .tech-available { background: rgba(243, 156, 18, 0.3); }
        .tech-locked { background: rgba(127, 140, 141, 0.3); }
    </style>
</head>
<body>
    <h1>Technology System Test</h1>
    
    <div class="test-section">
        <h2>Test Controls</h2>
        <button onclick="runAllTests()">Run All Tests</button>
        <button onclick="resetGame()">Reset Game</button>
        <button onclick="giveResources()">Give Test Resources</button>
        <button onclick="displayTechTree()">Display Tech Tree</button>
    </div>

    <div class="test-section">
        <h2>Test Results</h2>
        <div id="test-results"></div>
    </div>

    <div class="test-section">
        <h2>Technology Tree Status</h2>
        <div id="tech-tree-display" class="tech-tree-display"></div>
    </div>

    <div class="test-section">
        <h2>Game State</h2>
        <div id="game-state-display"></div>
    </div>

    <!-- Include game scripts -->
    <script src="js/utils.js"></script>
    <script src="js/gameEngine.js"></script>
    <script src="js/technologyManager.js"></script>

    <script>
        // Initialize game components for testing
        let gameEngine;
        let technologyManager;

        function initializeGame() {
            gameEngine = new GameEngine();
            technologyManager = new TechnologyManager(gameEngine);
            gameEngine.setTechnologyManager(technologyManager);
            
            // Give some initial resources for testing
            gameEngine.gameState.resources.knowledge = 100;
            gameEngine.gameState.resources.industry = 100;
            gameEngine.gameState.resources.food = 100;
            
            console.log('Game initialized for testing');
        }

        function runAllTests() {
            const results = [];
            
            // Initialize fresh game state
            initializeGame();
            
            // Test 1: Technology Manager Initialization
            results.push({
                test: "TechnologyManager should initialize correctly",
                passed: technologyManager && typeof technologyManager.technologyTree === 'object'
            });

            // Test 2: Technology Tree Structure
            const techTree = technologyManager.technologyTree;
            results.push({
                test: "Technology tree should contain basic technologies",
                passed: techTree.basicAgriculture && techTree.industrialization && techTree.education
            });

            // Test 3: Available Technologies
            const availableTechs = technologyManager.getAvailableTechnologies();
            results.push({
                test: "Should have available technologies at start",
                passed: availableTechs.length > 0
            });

            // Test 4: Research Cost Checking
            const canAffordBasicAg = technologyManager.canAffordResearch(techTree.basicAgriculture.cost);
            results.push({
                test: "Should be able to afford basic agriculture with test resources",
                passed: canAffordBasicAg
            });

            // Test 5: Research Prerequisites
            const canResearchBasicAg = technologyManager.canResearch('basicAgriculture');
            results.push({
                test: "Should be able to research basic agriculture (no prerequisites)",
                passed: canResearchBasicAg
            });

            // Test 6: Research Prerequisites Blocking
            const canResearchIrrigation = technologyManager.canResearch('irrigation');
            results.push({
                test: "Should NOT be able to research irrigation (requires basic agriculture)",
                passed: !canResearchIrrigation
            });

            // Test 7: Start Research
            const researchStarted = technologyManager.startResearch('basicAgriculture');
            results.push({
                test: "Should be able to start research on basic agriculture",
                passed: researchStarted
            });

            // Test 8: Research In Progress
            const currentResearch = technologyManager.getCurrentResearchStatus();
            results.push({
                test: "Should have research in progress",
                passed: currentResearch && currentResearch.id === 'basicAgriculture'
            });

            // Test 9: Cannot Start Multiple Research
            const secondResearchBlocked = !technologyManager.startResearch('industrialization');
            results.push({
                test: "Should not be able to start second research while one is in progress",
                passed: secondResearchBlocked
            });

            // Test 10: Complete Research (simulate)
            gameEngine.gameState.technologies.researchProgress = 1.0;
            technologyManager.completeResearch('basicAgriculture');
            const isResearched = gameEngine.gameState.technologies.researched.includes('basicAgriculture');
            results.push({
                test: "Should complete research and mark as researched",
                passed: isResearched
            });

            // Test 11: Technology Effects Applied
            const farmGenerator = gameEngine.gameState.generators.farm;
            const hasMultiplier = farmGenerator.technologyMultipliers && 
                                farmGenerator.technologyMultipliers.basicAgriculture === 1.25;
            results.push({
                test: "Should apply technology multiplier to farm generator",
                passed: hasMultiplier
            });

            // Test 12: Updated Available Technologies
            technologyManager.updateAvailableTechnologies();
            const newAvailableTechs = technologyManager.getAvailableTechnologies();
            const irrigationAvailable = newAvailableTechs.some(tech => tech.id === 'irrigation');
            results.push({
                test: "Should unlock irrigation after researching basic agriculture",
                passed: irrigationAvailable
            });

            // Test 13: Production Calculation with Technology
            const productionWithTech = gameEngine.calculateGeneratorProduction('farm');
            gameEngine.gameState.generators.farm.count = 1;
            const expectedProduction = 1 * 1 * 1.25; // base * count * tech multiplier
            results.push({
                test: "Should calculate production with technology multiplier",
                passed: Math.abs(productionWithTech - expectedProduction) < 0.01
            });

            // Test 14: Technology Tree Display Data
            const treeDisplayData = technologyManager.getTechnologyTreeForDisplay();
            const basicAgData = treeDisplayData.basicAgriculture;
            results.push({
                test: "Should provide correct display data for researched technology",
                passed: basicAgData && basicAgData.isResearched && !basicAgData.canResearch
            });

            // Test 15: Generator Unlock Technology
            gameEngine.gameState.resources.knowledge = 50; // Enough for industrialization
            const industrializationStarted = technologyManager.startResearch('industrialization');
            if (industrializationStarted) {
                gameEngine.gameState.technologies.researchProgress = 1.0;
                technologyManager.completeResearch('industrialization');
            }
            const factoryUnlocked = gameEngine.gameState.generators.factory.unlocked;
            results.push({
                test: "Should unlock factory generator after researching industrialization",
                passed: factoryUnlocked
            });

            displayResults(results);
            displayGameState();
            displayTechTree();
        }

        function displayResults(results) {
            const container = document.getElementById('test-results');
            let html = '';
            
            let passed = 0;
            let total = results.length;
            
            results.forEach(result => {
                const className = result.passed ? 'test-pass' : 'test-fail';
                const status = result.passed ? 'PASS' : 'FAIL';
                if (result.passed) passed++;
                
                html += `<div class="${className} test-result">
                    <strong>[${status}]</strong> ${result.test}
                </div>`;
            });
            
            html = `<div class="test-info test-result">
                <strong>Test Summary: ${passed}/${total} tests passed</strong>
            </div>` + html;
            
            container.innerHTML = html;
        }

        function displayGameState() {
            if (!gameEngine) return;
            
            const gameState = gameEngine.gameState;
            const container = document.getElementById('game-state-display');
            
            container.innerHTML = `
                <h4>Resources:</h4>
                <p>GDP: ${NumberFormatter.format(gameState.gdp)}</p>
                <p>Knowledge: ${NumberFormatter.format(gameState.resources.knowledge)}</p>
                <p>Industry: ${NumberFormatter.format(gameState.resources.industry)}</p>
                <p>Food: ${NumberFormatter.format(gameState.resources.food)}</p>
                
                <h4>Technologies:</h4>
                <p>Researched: ${gameState.technologies.researched.join(', ') || 'None'}</p>
                <p>Available: ${gameState.technologies.available.join(', ') || 'None'}</p>
                <p>In Progress: ${gameState.technologies.inProgress || 'None'}</p>
                
                <h4>Generators:</h4>
                <p>Farm: ${gameState.generators.farm.count} (Unlocked: ${gameState.generators.farm.unlocked})</p>
                <p>Factory: ${gameState.generators.factory.count} (Unlocked: ${gameState.generators.factory.unlocked})</p>
                <p>School: ${gameState.generators.school.count} (Unlocked: ${gameState.generators.school.unlocked})</p>
            `;
        }

        function displayTechTree() {
            if (!technologyManager) return;
            
            const treeData = technologyManager.getTechnologyTreeForDisplay();
            const container = document.getElementById('tech-tree-display');
            let html = '';
            
            for (const [techId, tech] of Object.entries(treeData)) {
                let className = 'tech-locked';
                if (tech.isResearched) className = 'tech-researched';
                else if (tech.isAvailable) className = 'tech-available';
                
                const costStr = Object.entries(tech.cost)
                    .map(([resource, amount]) => `${amount} ${resource}`)
                    .join(', ');
                
                html += `<div class="${className} tech-item">
                    <strong>${tech.name}</strong> (Tier ${tech.tier}) - Cost: ${costStr}
                    <br><small>${tech.description}</small>
                </div>`;
            }
            
            container.innerHTML = html;
        }

        function resetGame() {
            initializeGame();
            displayGameState();
            displayTechTree();
            document.getElementById('test-results').innerHTML = '<p>Game reset. Run tests to see results.</p>';
        }

        function giveResources() {
            if (!gameEngine) initializeGame();
            
            gameEngine.gameState.gdp = 10000;
            gameEngine.gameState.resources.knowledge = 1000;
            gameEngine.gameState.resources.industry = 1000;
            gameEngine.gameState.resources.food = 1000;
            gameEngine.gameState.resources.influence = 100;
            gameEngine.gameState.resources.bitcoin = 100;
            
            displayGameState();
        }

        // Initialize on page load
        window.addEventListener('load', () => {
            initializeGame();
            displayGameState();
            displayTechTree();
        });
    </script>
</body>
</html>
