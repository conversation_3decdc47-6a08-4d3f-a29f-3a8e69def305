/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background: linear-gradient(135deg, #2c3e50, #34495e);
    color: #ecf0f1;
    min-height: 100vh;
}

/* Login Page Styles */
.login-page {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
}

.login-container {
    background: rgba(52, 73, 94, 0.9);
    padding: 2rem;
    border-radius: 10px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    text-align: center;
    max-width: 400px;
    width: 90%;
}

.game-logo h1 {
    font-size: 2.5rem;
    color: #f39c12;
    margin-bottom: 0.5rem;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.subtitle {
    color: #bdc3c7;
    font-style: italic;
    margin-bottom: 2rem;
}

.login-form h2 {
    margin-bottom: 1.5rem;
    color: #ecf0f1;
}

.login-buttons {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.login-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 5px;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.google-btn {
    background: #db4437;
    color: white;
}

.google-btn:hover {
    background: #c23321;
}

.apple-btn {
    background: #000;
    color: white;
}

.apple-btn:hover {
    background: #333;
}

.login-status {
    margin-top: 1rem;
    padding: 0.5rem;
    border-radius: 5px;
    min-height: 1rem;
}

.login-status.error {
    background: rgba(231, 76, 60, 0.2);
    color: #e74c3c;
}

.login-status.success {
    background: rgba(46, 204, 113, 0.2);
    color: #2ecc71;
}

/* Game Page Styles */
.game-page {
    background: linear-gradient(135deg, #1a252f, #2c3e50);
}

.game-container {
    max-width: 1200px;
    margin: 0 auto;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Header */
.game-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 2rem;
    background: rgba(52, 73, 94, 0.8);
    border-bottom: 2px solid #f39c12;
}

.game-title h1 {
    font-size: 2rem;
    color: #f39c12;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.user-info {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.logout-btn {
    padding: 0.5rem 1rem;
    background: #e74c3c;
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    transition: background 0.3s ease;
}

.logout-btn:hover {
    background: #c0392b;
}

/* Main Game Area */
.game-main {
    flex: 1;
    padding: 2rem;
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

/* GDP Section */
.gdp-section {
    text-align: center;
    background: rgba(52, 73, 94, 0.6);
    padding: 2rem;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.gdp-display h2 {
    font-size: 2.5rem;
    color: #2ecc71;
    margin-bottom: 0.5rem;
}

.gdp-display p {
    color: #bdc3c7;
    margin-bottom: 1.5rem;
}

.gdp-click-btn {
    padding: 1rem 2rem;
    font-size: 1.2rem;
    background: linear-gradient(45deg, #f39c12, #e67e22);
    color: white;
    border: none;
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 5px 15px rgba(243, 156, 18, 0.3);
}

.gdp-click-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(243, 156, 18, 0.4);
}

.gdp-click-btn:active {
    transform: translateY(0);
}

/* Tabbed Interface */
.game-tabs {
    background: rgba(52, 73, 94, 0.6);
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.tab-buttons {
    display: flex;
    background: rgba(44, 62, 80, 0.8);
}

.tab-btn {
    flex: 1;
    padding: 1rem;
    background: transparent;
    color: #bdc3c7;
    border: none;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border-bottom: 3px solid transparent;
    position: relative;
    overflow: hidden;
}

.tab-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(243, 156, 18, 0.2), transparent);
    transition: left 0.5s ease;
}

.tab-btn:hover {
    background: rgba(52, 73, 94, 0.5);
    color: #ecf0f1;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.tab-btn:hover::before {
    left: 100%;
}

.tab-btn.active {
    color: #f39c12;
    border-bottom-color: #f39c12;
    background: rgba(52, 73, 94, 0.3);
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.tab-btn:active {
    transform: translateY(0);
    transition: transform 0.1s ease;
}

.tab-content {
    min-height: 400px;
    position: relative;
    overflow: hidden;
}

.tab-panel {
    display: none;
    padding: 2rem;
    opacity: 0;
    transform: translateX(20px);
    transition: opacity 0.3s ease, transform 0.3s ease;
}

.tab-panel.active {
    display: block;
    opacity: 1;
    transform: translateX(0);
    animation: slideInTab 0.3s ease-out;
}

@keyframes slideInTab {
    from {
        opacity: 0;
        transform: translateX(20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.tab-panel h3 {
    color: #f39c12;
    margin-bottom: 1.5rem;
    font-size: 1.5rem;
}

/* Tab notification badges */
.tab-notification-badge {
    position: absolute;
    top: 8px;
    right: 8px;
    background: #e74c3c;
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    font-size: 0.75rem;
    font-weight: bold;
    display: flex;
    align-items: center;
    justify-content: center;
    animation: pulse 2s infinite;
    z-index: 1;
}

.tab-notification-badge.warning {
    background: #f39c12;
}

.tab-notification-badge.info {
    background: #3498db;
}

.tab-notification-badge.success {
    background: #27ae60;
}

@keyframes pulse {
    0% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.1);
        opacity: 0.8;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

/* Event Display Styles */
.event-section {
    margin-bottom: 2rem;
}

.event-section-title {
    color: #f39c12;
    font-size: 1.2rem;
    margin-bottom: 1rem;
    border-bottom: 2px solid #f39c12;
    padding-bottom: 0.5rem;
}

.event-card {
    background: rgba(44, 62, 80, 0.6);
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1rem;
    border-left: 4px solid #3498db;
    transition: all 0.3s ease;
}

.event-card:hover {
    background: rgba(44, 62, 80, 0.8);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.active-event {
    border-left-color: #e74c3c;
    animation: eventPulse 3s infinite;
}

.history-event.resolved {
    border-left-color: #27ae60;
    opacity: 0.8;
}

.history-event.expired {
    border-left-color: #95a5a6;
    opacity: 0.6;
}

.history-event.pending {
    border-left-color: #f39c12;
}

.event-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
}

.event-name {
    color: #ecf0f1;
    margin: 0;
    font-size: 1rem;
}

.event-category, .event-status {
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: bold;
    text-transform: uppercase;
}

.event-category.localCrisis, .event-status.pending {
    background: #e74c3c;
    color: white;
}

.event-category.internationalPolitics {
    background: #3498db;
    color: white;
}

.event-category.imfLoan {
    background: #f39c12;
    color: white;
}

.event-status.resolved {
    background: #27ae60;
    color: white;
}

.event-status.expired {
    background: #95a5a6;
    color: white;
}

.event-description {
    color: #bdc3c7;
    margin-bottom: 0.5rem;
    line-height: 1.4;
}

.event-timer, .event-effects {
    color: #ecf0f1;
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
}

.event-timestamp {
    color: #95a5a6;
    font-size: 0.8rem;
    font-style: italic;
}

.event-actions {
    margin-top: 1rem;
}

.event-details-btn {
    background: #3498db;
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.9rem;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.event-details-btn::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.3s ease, height 0.3s ease;
}

.event-details-btn:hover {
    background: #2980b9;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(52, 152, 219, 0.3);
}

.event-details-btn:hover::before {
    width: 100%;
    height: 100%;
}

.event-details-btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 6px rgba(52, 152, 219, 0.2);
}

.no-events {
    color: #95a5a6;
    font-style: italic;
    text-align: center;
    padding: 2rem;
}

@keyframes eventPulse {
    0%, 100% {
        border-left-color: #e74c3c;
    }
    50% {
        border-left-color: #c0392b;
    }
}

/* Enhanced Visual Feedback Animations */
@keyframes ripple {
    to {
        transform: scale(4);
        opacity: 0;
    }
}

.feedback-success {
    animation: feedbackSuccess 0.3s ease;
}

.feedback-error {
    animation: feedbackError 0.3s ease;
}

.feedback-info {
    animation: feedbackInfo 0.3s ease;
}

@keyframes feedbackSuccess {
    0% {
        background-color: inherit;
        transform: scale(1);
    }
    50% {
        background-color: rgba(39, 174, 96, 0.3);
        transform: scale(1.05);
    }
    100% {
        background-color: inherit;
        transform: scale(1);
    }
}

@keyframes feedbackError {
    0% {
        background-color: inherit;
        transform: scale(1);
    }
    50% {
        background-color: rgba(231, 76, 60, 0.3);
        transform: scale(1.05);
    }
    100% {
        background-color: inherit;
        transform: scale(1);
    }
}

@keyframes feedbackInfo {
    0% {
        background-color: inherit;
        transform: scale(1);
    }
    50% {
        background-color: rgba(52, 152, 219, 0.3);
        transform: scale(1.05);
    }
    100% {
        background-color: inherit;
        transform: scale(1);
    }
}

/* Enhanced hover states for interactive elements */
.purchase-btn, .upgrade-btn, .research-btn {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.purchase-btn:hover, .upgrade-btn:hover, .research-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.purchase-btn:active, .upgrade-btn:active, .research-btn:active {
    transform: translateY(0);
    transition: transform 0.1s ease;
}

/* Resource and Generator Lists */
.resource-list,
.generator-list {
    display: grid;
    gap: 1rem;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

.resource-item,
.generator-item {
    background: rgba(44, 62, 80, 0.6);
    padding: 1rem;
    border-radius: 8px;
    border-left: 4px solid #3498db;
}

.generator-item {
    border-left-color: #2ecc71;
}

/* Footer */
.game-footer {
    background: rgba(52, 73, 94, 0.8);
    padding: 1rem 2rem;
    border-top: 2px solid #f39c12;
}

.game-stats {
    display: flex;
    justify-content: space-around;
    color: #bdc3c7;
}

/* Modal Styles */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.modal.hidden {
    display: none;
}

.modal-content {
    background: #34495e;
    padding: 2rem;
    border-radius: 10px;
    max-width: 500px;
    width: 90%;
    text-align: center;
}

.event-choices {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-top: 1.5rem;
}

.event-choice-btn {
    padding: 0.75rem 1.5rem;
    background: #3498db;
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    transition: background 0.3s ease;
}

.event-choice-btn:hover {
    background: #2980b9;
}

.event-choice-btn.disabled {
    background: #7f8c8d;
    cursor: not-allowed;
}

.event-choice-btn.disabled:hover {
    background: #7f8c8d;
}

.choice-container {
    background: rgba(52, 73, 94, 0.3);
    padding: 1rem;
    border-radius: 8px;
    border-left: 4px solid #3498db;
}

.choice-description {
    font-size: 0.9rem;
    color: #bdc3c7;
    margin-top: 0.5rem;
    line-height: 1.4;
}

.choice-cost {
    font-size: 0.8rem;
    color: #f39c12;
    margin-top: 0.5rem;
    font-weight: bold;
}

/* Loading Screen */
.loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #2c3e50, #34495e);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 2000;
}

.loading-content {
    text-align: center;
}

.loading-content h2 {
    color: #f39c12;
    margin-bottom: 2rem;
    font-size: 2rem;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 5px solid rgba(243, 156, 18, 0.3);
    border-top: 5px solid #f39c12;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Utility Classes */
.hidden {
    display: none !important;
}

.disabled {
    opacity: 0.5;
    cursor: not-allowed !important;
}

/* Responsive Design */
@media (max-width: 768px) {
    .game-header {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }
    
    .game-main {
        padding: 1rem;
    }
    
    .tab-buttons {
        flex-wrap: wrap;
    }
    
    .tab-btn {
        min-width: 50%;
    }
    
    .game-stats {
        flex-direction: column;
        gap: 0.5rem;
        text-align: center;
    }
    
    .resource-list,
    .generator-list {
        grid-template-columns: 1fr;
    }

    /* Enhanced tab responsiveness */
    .tab-content {
        min-height: 300px;
    }

    .tab-panel {
        padding: 1rem;
    }

    /* Event cards responsive design */
    .event-card {
        padding: 0.75rem;
        margin-bottom: 0.75rem;
    }

    .event-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }

    .event-category, .event-status {
        align-self: flex-end;
    }

    .event-actions {
        margin-top: 0.75rem;
    }

    .event-details-btn {
        width: 100%;
        padding: 0.75rem;
    }

    /* Tab notification badges responsive */
    .tab-notification-badge {
        top: 4px;
        right: 4px;
        width: 16px;
        height: 16px;
        font-size: 0.65rem;
    }
}

/* Additional responsive breakpoint for very small screens */
@media (max-width: 480px) {
    .tab-btn {
        min-width: 100%;
        font-size: 0.9rem;
        padding: 0.75rem;
    }

    .tab-buttons {
        flex-direction: column;
    }

    .game-container {
        padding: 0.5rem;
    }

    .gdp-section {
        padding: 1rem;
    }

    .gdp-click-btn {
        padding: 1rem 2rem;
        font-size: 1rem;
    }

    .event-section-title {
        font-size: 1rem;
    }

    .event-name {
        font-size: 0.9rem;
    }

    .event-description {
        font-size: 0.85rem;
    }
}

/* Landscape orientation optimizations */
@media (max-width: 768px) and (orientation: landscape) {
    .game-header {
        padding: 0.5rem 1rem;
    }

    .game-header h1 {
        font-size: 1.5rem;
    }

    .subtitle {
        font-size: 0.8rem;
    }

    .tab-content {
        min-height: 250px;
    }

    .gdp-section {
        padding: 0.75rem;
    }

    .gdp-display h2 {
        font-size: 1.5rem;
    }

    .gdp-click-btn {
        padding: 0.75rem 1.5rem;
        font-size: 0.9rem;
    }
}

/* High DPI display optimizations */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .tab-notification-badge {
        border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .event-card {
        border-left-width: 3px;
    }
}

/* Loading Indicator */
.loading-indicator {
    text-align: center;
    padding: 2rem;
    color: #bdc3c7;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(243, 156, 18, 0.3);
    border-top: 4px solid #f39c12;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
}

.auth-error {
    margin-top: 1rem;
    padding: 0.75rem;
    background: rgba(231, 76, 60, 0.2);
    color: #e74c3c;
    border-radius: 5px;
    border-left: 4px solid #e74c3c;
}

.error-message {
    margin-top: 1rem;
    padding: 0.75rem;
    background: rgba(231, 76, 60, 0.2);
    color: #e74c3c;
    border-radius: 5px;
    text-align: center;
}

/* Bedrock Widget Styling Overrides */
#bedrock-login-widget {
    margin: 1rem 0;
}

#bedrock-login-widget .container {
    background: rgba(44, 62, 80, 0.8) !important;
    border: 1px solid rgba(243, 156, 18, 0.3) !important;
}

#bedrock-login-widget button {
    transition: all 0.3s ease !important;
}

#bedrock-login-widget button:hover {
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2) !important;
}

/* Fallback Authentication UI */
.fallback-auth {
    text-align: center;
    padding: 2rem;
    background: rgba(44, 62, 80, 0.8);
    border-radius: 10px;
    border: 1px solid rgba(243, 156, 18, 0.3);
}

.fallback-auth h3 {
    color: #f39c12;
    margin-bottom: 1rem;
    font-size: 1.5rem;
}

.fallback-auth p {
    color: #bdc3c7;
    margin-bottom: 1.5rem;
}

.fallback-buttons {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-bottom: 1rem;
}

.fallback-note {
    font-size: 0.9rem;
    color: #95a5a6;
    font-style: italic;
}

/* Animation Classes */
.click-animation {
    animation: clickPulse 0.3s ease;
}

@keyframes clickPulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.purchase-animation {
    animation: purchaseGlow 0.5s ease;
}

@keyframes purchaseGlow {
    0% { box-shadow: 0 0 5px rgba(46, 204, 113, 0.5); }
    50% { box-shadow: 0 0 20px rgba(46, 204, 113, 0.8); }
    100% { box-shadow: 0 0 5px rgba(46, 204, 113, 0.5); }
}
/* U
I Manager Additions */
.generator-details, .resource-details {
    margin-bottom: 1rem;
}

.generator-count, .generator-production, .resource-amount, .resource-rate, .resource-total {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
}

.label {
    color: #bdc3c7;
}

.value {
    color: #ecf0f1;
    font-weight: bold;
}

.generator-purchase, .generator-upgrade {
    background: rgba(44, 62, 80, 0.5);
    padding: 0.75rem;
    border-radius: 5px;
    margin-bottom: 0.75rem;
}

.purchase-info, .upgrade-info, .upgrade-cost {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
}

.purchase-buttons {
    display: flex;
    gap: 0.5rem;
}

.purchase-btn, .upgrade-btn {
    padding: 0.5rem;
    background: linear-gradient(45deg, #3498db, #2980b9);
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    transition: all 0.3s ease;
    flex: 1;
}

.purchase-btn:hover, .upgrade-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.purchase-btn:active, .upgrade-btn:active {
    transform: translateY(0);
}

.upgrade-btn {
    background: linear-gradient(45deg, #9b59b6, #8e44ad);
    width: 100%;
    margin-top: 0.5rem;
}

.game-notification {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

/* Floating number animation */
@keyframes float-up {
    0% { opacity: 1; transform: translate(-50%, -50%); }
    100% { opacity: 0; transform: translate(-50%, -150%); }
}

.floating-number {
    position: absolute;
    pointer-events: none;
    z-index: 1000;
    animation: float-up 1s ease-out forwards;
}

/* Technology Tree Styles */
.current-research {
    background: rgba(52, 152, 219, 0.2);
    border: 1px solid #3498db;
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1.5rem;
}

.current-research h4 {
    color: #3498db;
    margin-bottom: 0.75rem;
    font-size: 1.1rem;
}

.research-item {
    background: rgba(44, 62, 80, 0.5);
    padding: 0.75rem;
    border-radius: 5px;
}

.research-name {
    font-weight: bold;
    color: #ecf0f1;
    margin-bottom: 0.5rem;
}

.research-progress {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: rgba(44, 62, 80, 0.8);
    border-radius: 4px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #3498db, #2980b9);
    transition: width 0.3s ease;
}

.progress-text {
    font-size: 0.9rem;
    color: #bdc3c7;
    text-align: center;
}

.tech-tier {
    margin-bottom: 2rem;
}

.tech-tier h4 {
    color: #f39c12;
    margin-bottom: 1rem;
    font-size: 1.2rem;
    border-bottom: 2px solid rgba(243, 156, 18, 0.3);
    padding-bottom: 0.5rem;
}

.tech-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1rem;
}

.tech-card {
    background: rgba(44, 62, 80, 0.8);
    border: 1px solid rgba(149, 165, 166, 0.3);
    border-radius: 8px;
    padding: 1rem;
    transition: all 0.3s ease;
    position: relative;
}

.tech-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.tech-card.researched {
    background: rgba(46, 204, 113, 0.2);
    border-color: #2ecc71;
}

.tech-card.in-progress {
    background: rgba(52, 152, 219, 0.2);
    border-color: #3498db;
    animation: pulse 2s infinite;
}

.tech-card.available {
    background: rgba(243, 156, 18, 0.2);
    border-color: #f39c12;
}

.tech-card.unlocked {
    background: rgba(155, 89, 182, 0.2);
    border-color: #9b59b6;
}

.tech-card.locked {
    background: rgba(127, 140, 141, 0.2);
    border-color: #7f8c8d;
    opacity: 0.6;
}

.tech-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 0.75rem;
}

.tech-name {
    color: #ecf0f1;
    font-size: 1.1rem;
    margin: 0;
    flex: 1;
}

.tech-tier-badge {
    background: rgba(243, 156, 18, 0.8);
    color: #2c3e50;
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: bold;
    margin-left: 0.5rem;
}

.tech-description {
    color: #bdc3c7;
    font-size: 0.9rem;
    margin-bottom: 0.75rem;
    line-height: 1.4;
}

.tech-cost, .tech-prerequisites, .tech-unlocks {
    font-size: 0.85rem;
    margin-bottom: 0.5rem;
}

.tech-cost strong, .tech-prerequisites strong, .tech-unlocks strong {
    color: #ecf0f1;
}

.tech-cost {
    color: #f39c12;
}

.tech-prerequisites {
    color: #e74c3c;
}

.tech-unlocks {
    color: #2ecc71;
}

.tech-actions {
    margin-top: 1rem;
}

.research-btn {
    width: 100%;
    padding: 0.75rem;
    border: none;
    border-radius: 5px;
    font-size: 0.9rem;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
}

.research-btn.researched {
    background: #2ecc71;
    color: white;
    cursor: default;
}

.research-btn.in-progress {
    background: #3498db;
    color: white;
    cursor: default;
}

.research-btn.available {
    background: linear-gradient(45deg, #f39c12, #e67e22);
    color: white;
}

.research-btn.available:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.research-btn.unlocked {
    background: #7f8c8d;
    color: #bdc3c7;
    cursor: not-allowed;
}

.research-btn.locked {
    background: #34495e;
    color: #7f8c8d;
    cursor: not-allowed;
}

.research-btn:disabled {
    cursor: not-allowed;
    opacity: 0.6;
}

.tech-progress {
    margin-top: 0.75rem;
}

@keyframes pulse {
    0% { box-shadow: 0 0 0 0 rgba(52, 152, 219, 0.7); }
    70% { box-shadow: 0 0 0 10px rgba(52, 152, 219, 0); }
    100% { box-shadow: 0 0 0 0 rgba(52, 152, 219, 0); }
}

.research-animation {
    animation: researchStart 0.5s ease;
}

@keyframes researchStart {
    0% { transform: scale(1); background-color: #f39c12; }
    50% { transform: scale(1.05); background-color: #3498db; }
    100% { transform: scale(1); background-color: #3498db; }
}

/* Responsive Technology Tree */
@media (max-width: 768px) {
    .tech-grid {
        grid-template-columns: 1fr;
    }

    .tech-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }

    .tech-tier-badge {
        margin-left: 0;
    }
}

/* Enhanced Animation Styles for AudioManager and AnimationManager */

/* Resource gain animations */
@keyframes resource-gain {
    0% { transform: translate(-50%, -50%) scale(1); opacity: 1; }
    50% { transform: translate(-50%, -75%) scale(1.2); opacity: 0.8; }
    100% { transform: translate(-50%, -100%) scale(0.8); opacity: 0; }
}

.floating-resource-gain {
    position: absolute;
    pointer-events: none;
    z-index: 1000;
    font-weight: bold;
    text-shadow: 0 0 3px rgba(0,0,0,0.7);
}

/* Purchase and upgrade animations */
@keyframes purchase-glow {
    0% { box-shadow: 0 0 5px rgba(46, 204, 113, 0.5); }
    50% { box-shadow: 0 0 20px rgba(46, 204, 113, 0.8); }
    100% { box-shadow: 0 0 5px rgba(46, 204, 113, 0.5); }
}

@keyframes upgrade-spin {
    0% { transform: rotate(0deg) scale(1); }
    50% { transform: rotate(180deg) scale(1.2); }
    100% { transform: rotate(360deg) scale(1); }
}

/* Event animations */
@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

/* Milestone celebration animations */
@keyframes milestone-celebration {
    0% { transform: scale(1) rotate(0deg); opacity: 1; }
    25% { transform: scale(1.2) rotate(5deg); opacity: 0.9; }
    50% { transform: scale(1.3) rotate(-5deg); opacity: 0.8; }
    75% { transform: scale(1.1) rotate(3deg); opacity: 0.9; }
    100% { transform: scale(1) rotate(0deg); opacity: 1; }
}

/* Animation utility classes */
.animation-glow {
    box-shadow: 0 0 15px rgba(46, 204, 113, 0.6);
    transition: box-shadow 0.3s ease;
}

.animation-highlight {
    background-color: rgba(255, 255, 0, 0.2);
    transition: background-color 0.3s ease;
}

/* Particle effects */
.particle {
    position: absolute;
    width: 4px;
    height: 4px;
    background: radial-gradient(circle, #ffd700, #ff6b35);
    border-radius: 50%;
    pointer-events: none;
    z-index: 1000;
}

/* Victory notification enhancements */
.game-notification.victory {
    background: linear-gradient(45deg, #ffd700, #ffed4e) !important;
    color: #2c3e50 !important;
    font-size: 1.2rem !important;
    font-weight: bold !important;
    border: 2px solid #f39c12 !important;
    box-shadow: 0 0 20px rgba(255, 215, 0, 0.6) !important;
    animation: victory-pulse 2s infinite;
}

@keyframes victory-pulse {
    0% {
        transform: scale(1);
        box-shadow: 0 0 20px rgba(255, 215, 0, 0.6);
    }
    50% {
        transform: scale(1.05);
        box-shadow: 0 0 30px rgba(255, 215, 0, 0.8);
    }
    100% {
        transform: scale(1);
        box-shadow: 0 0 20px rgba(255, 215, 0, 0.6);
    }
}

/* Enhanced button feedback */
.purchase-btn.animation-glow,
.upgrade-btn.animation-glow,
.research-btn.animation-glow {
    animation: purchase-glow 0.5s ease;
}

/* Screen flash effect */
.screen-flash {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(255, 255, 255, 0.3);
    pointer-events: none;
    z-index: 9999;
    animation: flash 0.2s ease-out;
}

@keyframes flash {
    0% { opacity: 0; }
    50% { opacity: 1; }
    100% { opacity: 0; }
}

/* Victory Screen Styles */
.victory-modal {
    z-index: 10000;
    background: rgba(0, 0, 0, 0.9);
}

.victory-content {
    background: linear-gradient(135deg, #2c3e50, #34495e);
    border: 3px solid #f39c12;
    border-radius: 15px;
    max-width: 600px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.8);
    animation: victory-entrance 1s ease-out;
}

@keyframes victory-entrance {
    0% {
        opacity: 0;
        transform: scale(0.8) translateY(-50px);
    }
    100% {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

.victory-header {
    text-align: center;
    padding: 2rem 2rem 1rem;
    background: linear-gradient(135deg, #f39c12, #e67e22);
    border-radius: 12px 12px 0 0;
    margin: -1px -1px 0 -1px;
}

.victory-header h2 {
    font-size: 2.5rem;
    color: #2c3e50;
    margin: 0 0 0.5rem;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
    animation: victory-title-glow 2s infinite alternate;
}

@keyframes victory-title-glow {
    0% {
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
    }
    100% {
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3), 0 0 20px rgba(255, 255, 255, 0.5);
    }
}

.victory-header p {
    font-size: 1.2rem;
    color: #2c3e50;
    margin: 0;
    font-weight: bold;
}

.victory-stats {
    padding: 2rem;
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 2rem;
    align-items: start;
}

.victory-score {
    text-align: center;
    background: rgba(52, 152, 219, 0.1);
    border: 2px solid #3498db;
    border-radius: 10px;
    padding: 1.5rem;
}

.victory-score h3 {
    color: #3498db;
    margin: 0 0 1rem;
    font-size: 1.3rem;
}

.score-display {
    font-size: 3rem;
    font-weight: bold;
    color: #f39c12;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
    animation: score-pulse 2s infinite;
}

@keyframes score-pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.victory-details {
    background: rgba(46, 204, 113, 0.1);
    border: 2px solid #2ecc71;
    border-radius: 10px;
    padding: 1.5rem;
}

.stat-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.stat-row:last-child {
    border-bottom: none;
}

.stat-label {
    color: #bdc3c7;
    font-weight: bold;
}

.stat-value {
    color: #2ecc71;
    font-weight: bold;
    font-size: 1.1rem;
}

.victory-actions {
    padding: 1rem 2rem 2rem;
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

.victory-btn {
    padding: 1rem 2rem;
    border: none;
    border-radius: 8px;
    font-size: 1.1rem;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 150px;
}

.continue-btn {
    background: linear-gradient(135deg, #2ecc71, #27ae60);
    color: white;
}

.continue-btn:hover {
    background: linear-gradient(135deg, #27ae60, #229954);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(46, 204, 113, 0.4);
}

.new-game-btn {
    background: linear-gradient(135deg, #e74c3c, #c0392b);
    color: white;
}

.new-game-btn:hover {
    background: linear-gradient(135deg, #c0392b, #a93226);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(231, 76, 60, 0.4);
}

.share-btn {
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
}

.share-btn:hover {
    background: linear-gradient(135deg, #2980b9, #21618c);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(52, 152, 219, 0.4);
}

/* Responsive design for victory screen */
@media (max-width: 768px) {
    .victory-stats {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .victory-header h2 {
        font-size: 2rem;
    }

    .score-display {
        font-size: 2.5rem;
    }

    .victory-actions {
        flex-direction: column;
        align-items: center;
    }

    .victory-btn {
        width: 100%;
        max-width: 250px;
    }
}